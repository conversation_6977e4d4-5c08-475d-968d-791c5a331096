<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Step;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\IpidBundle\Service\IpidValidationService;
use Psr\Log\LoggerInterface;

class IpidValidationStep implements StepInterface
{
    private IpidValidationService $ipidValidationService;
    private LoggerInterface $logger;

    public function __construct(
        IpidValidationService $ipidValidationService,
        LoggerInterface $logger
    ) {
        $this->ipidValidationService = $ipidValidationService;
        $this->logger = $logger;
    }

    public function apply($transfer): bool
    {
        if (!$transfer instanceof TransferOut) {
            $this->logger->error('iPiD validation step requires TransferOut entity', [
                'transfer_type' => get_class($transfer)
            ]);
            return false;
        }

        $result = $this->ipidValidationService->validateTransfer($transfer);

        $this->logger->info('iPiD validation step completed', [
            'transfer_id' => $transfer->getId(),
            'validation_status' => $result->getStatus(),
            'can_proceed' => $result->canProceed(),
            'requires_review' => $result->requiresManualReview()
        ]);

        return $result->canProceed();
    }
} 