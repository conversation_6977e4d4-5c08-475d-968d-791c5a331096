<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Step;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\IpidBundle\Service\IpidValidationService;
use Psr\Log\LoggerInterface;

/**
 * iPiD Validation Step for Transfer Processing Pipeline
 *
 * Implements EU transfer validation according to requirements:
 * 1. Check if it's transfer to EU
 * 2. Check if validation was already done
 * 3. Build check request
 * 4. Check for existing cached response (30-day TTL)
 * 5. Send API request if needed (with encryption, error handling)
 * 6. Save response entity for reuse
 * 7. Link transfer to response using consent entity
 *
 * This step never throws errors - it creates consent without manual approval
 * when country is not in config or validation fails.
 */
class IpidValidationStep implements StepInterface
{
    private IpidValidationService $ipidValidationService;
    private LoggerInterface $logger;

    public function __construct(
        IpidValidationService $ipidValidationService,
        LoggerInterface $logger
    ) {
        $this->ipidValidationService = $ipidValidationService;
        $this->logger = $logger;
    }

    public function apply($transfer): bool
    {
        // Step 1: Validate input - only process TransferOut
        if (!$transfer instanceof TransferOut) {
            $transferType = is_object($transfer) ? get_class($transfer) : gettype($transfer);
            $transferId = (is_object($transfer) && method_exists($transfer, 'getId')) ? $transfer->getId() : 'unknown';

            $this->logger->debug('iPiD validation step skipped - not a TransferOut entity', [
                'transfer_type' => $transferType,
                'transfer_id' => $transferId
            ]);

            return true;
        }

        try {
            $result = $this->ipidValidationService->validateTransfer($transfer);

            $this->logger->info('iPiD validation step completed successfully', [
                'transfer_id' => $transfer->getId(),
                'validation_status' => $result->getStatus(),
                'is_successful' => $result->isSuccessful(),
                'has_consent' => $result->getConsent() !== null,
                'requires_manual_review' => $result->requiresManualReview()
            ]);
        } catch (\Throwable $exception) {
            // Handle any unexpected errors gracefully
            $this->logger->error('iPiD validation step encountered an error', [
                'transfer_id' => $transfer->getId(),
                'error_message' => $exception->getMessage(),
                'error_class' => get_class($exception),
                'trace' => $exception->getTraceAsString()
            ]);
        }

        return true;
    }
}
