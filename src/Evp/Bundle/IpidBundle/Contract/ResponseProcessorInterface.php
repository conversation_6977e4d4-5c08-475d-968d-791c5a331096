<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Contract;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\DTO\IpidValidationResponse;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;

interface ResponseProcessorInterface
{
    public function saveApiResponse(IpidValidationResponse $response, string $requestHash, string $country): IpidApiResponse;
    
    public function processValidationResult(
        TransferOut $transfer,
        IpidApiResponse $ipidApiResponse,
        IpidValidationRequest $request
    ): IpidValidationResult;
}
