<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Contract;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;

interface CacheManagerInterface
{
    public function calculateRequestHash(IpidValidationRequest $request, TransferOut $transfer): string;
    
    public function findValidCachedResponse(string $requestHash, string $country): ?IpidApiResponse;
}
