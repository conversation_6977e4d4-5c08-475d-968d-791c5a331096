# iPiD Country Configuration Guide

## Overview

The iPiD Bundle supports configurable country and region validation through XML configuration. Currently, only EU countries are enabled for VOP (Verification of Payee) validation.

## Current Configuration

The current configuration in `services.xml` enables only the EU region:

```xml
<parameter key="ipid.enabled_countries" type="collection"></parameter>
<parameter key="ipid.enabled_regions" type="collection">
    <parameter>EU</parameter>
</parameter>
```

This enables validation for the following EU countries using the VOP scheme:

### EU/VOP Countries (Currently Enabled)
- **Eurozone Countries**: AT, BE, BG, HR, CY, CZ, DK, EE, FI, FR, DE, GR, HU, IE, IT, LV, LT, LU, MT, NL, PL, PT, RO, SK, SI, ES, SE
- **SEPA/IBAN EU Countries**: IS, LI, NO, CH, SM, VA, MC, AD

## Adding Individual Countries

To enable specific countries, add them to the `ipid.enabled_countries` parameter:

```xml
<parameter key="ipid.enabled_countries" type="collection">
    <parameter>US</parameter>
    <parameter>GB</parameter>
    <parameter>CA</parameter>
</parameter>
```

## Adding Regions

To enable entire regions, add them to the `ipid.enabled_regions` parameter:

```xml
<parameter key="ipid.enabled_regions" type="collection">
    <parameter>EU</parameter>
    <parameter>NORTH_AMERICA</parameter>
    <parameter>UK</parameter>
</parameter>
```

## Available Regions

The following regions are defined in `IpidCountryRegistry`:

### EU
**Validation Scheme**: VOP  
**Countries**: AT, BE, BG, HR, CY, CZ, DK, EE, FI, FR, DE, GR, HU, IE, IT, LV, LT, LU, MT, NL, PL, PT, RO, SK, SI, ES, SE, IS, LI, NO, CH, SM, VA, MC, AD

### UK  
**Validation Scheme**: COP (Confirmation of Payee)  
**Countries**: GB, UK

### NORTH_AMERICA
**Validation Scheme**: GLOBAL  
**Countries**: US, CA, MX

### SOUTH_AMERICA
**Validation Scheme**: GLOBAL  
**Countries**: BR, AR, CL, CO, PE, UY, EC

### ASIA_PACIFIC  
**Validation Scheme**: GLOBAL  
**Countries**: CN, IN, ID, VN, MY, KR, BD, NP, PK

### AFRICA
**Validation Scheme**: GLOBAL  
**Countries**: NG, UG, ZA

### MIDDLE_EAST
**Validation Scheme**: GLOBAL  
**Countries**: SA, TR

### IBAN_COUNTRIES
**Validation Scheme**: GLOBAL (Logic-based validation)  
**Countries**: All 83 IBAN countries as per iPiD documentation

## Validation Schemes

### VOP (Verification of Payee)
- **Used by**: EU countries
- **Features**: Name and ID verification, IBAN validation
- **API Integration**: Uses VOP-specific fields and response codes

### COP (Confirmation of Payee)
- **Used by**: UK
- **Features**: Name verification, account validation
- **API Integration**: Uses COP-specific fields and response codes

### GLOBAL
- **Used by**: All other countries
- **Features**: Basic account and name validation
- **API Integration**: Uses standard validation fields

## Example Configurations

### EU Only (Current Production)
```xml
<parameter key="ipid.enabled_countries" type="collection"></parameter>
<parameter key="ipid.enabled_regions" type="collection">
    <parameter>EU</parameter>
</parameter>
```

### EU + UK + US
```xml
<parameter key="ipid.enabled_countries" type="collection">
    <parameter>US</parameter>
</parameter>
<parameter key="ipid.enabled_regions" type="collection">
    <parameter>EU</parameter>
    <parameter>UK</parameter>
</parameter>
```

### Global Coverage
```xml
<parameter key="ipid.enabled_countries" type="collection"></parameter>
<parameter key="ipid.enabled_regions" type="collection">
    <parameter>EU</parameter>
    <parameter>UK</parameter>
    <parameter>NORTH_AMERICA</parameter>
    <parameter>SOUTH_AMERICA</parameter>
    <parameter>ASIA_PACIFIC</parameter>
    <parameter>AFRICA</parameter>
    <parameter>MIDDLE_EAST</parameter>
</parameter>
```

### Specific Countries Only
```xml
<parameter key="ipid.enabled_countries" type="collection">
    <parameter>US</parameter>
    <parameter>GB</parameter>
    <parameter>CA</parameter>
    <parameter>DE</parameter>
    <parameter>FR</parameter>
</parameter>
<parameter key="ipid.enabled_regions" type="collection"></parameter>
```

## Testing Configuration

After changing the configuration:

1. **Clear cache**: `php bin/console cache:clear`
2. **Run tests**: `php bin/phpunit src/Evp/Bundle/IpidBundle/Tests/Unit/Service/IpidCountryCheckerTest.php`
3. **Check logs**: Look for validation attempts and country enablement logging

## Implementation Details

### Country Validation Flow

1. **Transfer initiated** → Extract country from IBAN/BIC
2. **Country check** → `IpidCountryChecker::supportsValidation()`
3. **Dual validation**:
   - Configuration check: Is country/region enabled?
   - Corridor check: Does iPiD API support this country?
4. **Proceed or reject** based on both checks

### Services Involved

- **`IpidCountryRegistry`**: Manages enabled countries/regions mapping
- **`IpidCountryChecker`**: Validates if countries are enabled
- **`IpidCorridorSpecification`**: Checks API support for countries
- **`IpidValidationService`**: Orchestrates validation with dual checks

### Configuration Safety

The system uses dual validation:
1. **Configuration check**: Is the country enabled in XML config?
2. **API support check**: Does the corridor specification support this country?

Both checks must pass for validation to proceed. This prevents:
- Validation attempts for unsupported countries
- Configuration errors enabling non-existent corridors
- Runtime errors from misconfiguration

## Monitoring and Logging

The validation service logs:
- **Successful validations**: Country, scheme, region information
- **Rejected attempts**: Country, enabled countries/regions, reason
- **Configuration state**: What countries/regions are currently enabled

Example log entry for rejected validation:
```
[WARNING] iPiD validation attempted for unsupported country: {
    "transfer_id": 123,
    "country": "US",
    "enabled_countries": ["DE", "FR", "IT", ...],
    "enabled_regions": ["EU"],
    "validation_scheme": "GLOBAL",
    "country_region": "NORTH_AMERICA"
}
```

## Future Enhancements

When enabling new countries/regions:

1. **Verify API support**: Ensure iPiD supports the corridor
2. **Update corridor specification**: Add country-specific field requirements
3. **Test thoroughly**: Especially scheme-specific features (VOP/COP vs Global)
4. **Monitor performance**: Some corridors have different response times
5. **Consider compliance**: Different regions may have different regulatory requirements 