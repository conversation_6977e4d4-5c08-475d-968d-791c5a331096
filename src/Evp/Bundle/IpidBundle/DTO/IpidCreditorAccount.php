<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidCreditorAccount
{
    private ?string $iban = null;
    private ?string $accountId = null;
    private ?string $currency = null;

    public function getIban(): ?string
    {
        return $this->iban;
    }

    public function setIban(?string $iban): self
    {
        $this->iban = $iban;
        return $this;
    }

    public function getAccountId(): ?string
    {
        return $this->accountId;
    }

    public function setAccountId(?string $accountId): self
    {
        $this->accountId = $accountId;
        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function hasIbanOrAccountId(): bool
    {
        return $this->iban !== null || $this->accountId !== null;
    }

    public function toArray(): array
    {
        $data = [];

        // According to API spec: Must have either IBAN or account Id. Will prioritize IBAN if both are supplied
        if ($this->iban !== null) {
            $data['iban'] = $this->iban;
        } elseif ($this->accountId !== null) {
            $data['account_id'] = $this->accountId;
        }

        if ($this->currency !== null) {
            $data['currency'] = $this->currency;
        }

        return $data;
    }
} 