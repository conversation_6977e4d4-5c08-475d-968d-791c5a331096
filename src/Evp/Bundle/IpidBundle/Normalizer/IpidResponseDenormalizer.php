<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Normalizer;

use Evp\Bundle\IpidBundle\DTO\IpidValidationResponse;

class IpidResponseDenormalizer
{
    public function denormalize(array $responseData, ?string $requestId = null): IpidValidationResponse
    {
        $this->validateResponseStructure($responseData);

        $responseCode = $responseData['response_code'] ?? IpidValidationResponse::CODE_GENERAL_EXCEPTION;
        $responseMessage = $responseData['response_message'] ?? 'Unknown error';

        $this->validateResponseCode($responseCode);
        $this->validateResponseMessage($responseMessage);

        $response = new IpidValidationResponse($responseCode, $responseMessage);

        // Process data section if it exists
        if (isset($responseData['data']) && is_array($responseData['data'])) {
            $data = $responseData['data'];

            $this->validateDataSection($data);

            if (isset($data['encrypted_payload'])) {
                $response->setEncryptedPayload($data['encrypted_payload']);
            }

            if (isset($data['match_score'])) {
                $matchScore = $this->validateAndNormalizeMatchScore($data['match_score']);
                $response->setMatchScore($matchScore);
            }

            if (isset($data['match_score_description'])) {
                $response->setMatchScoreDescription($data['match_score_description']);
            }

            if (isset($data['logic_score'])) {
                $logicScore = $this->validateAndNormalizeLogicScore($data['logic_score']);
                $response->setLogicScore($logicScore);
            }

            if (isset($data['logic_score_description'])) {
                $response->setLogicScoreDescription($data['logic_score_description']);
            }

            if (isset($data['public_key_hint']) && is_array($data['public_key_hint'])) {
                $response->setPublicKeyHint($data['public_key_hint']);
            }

            // Parse scheme-specific response fields from node_api.txt section 5.2
            if (isset($data['vop_id_match'])) {
                $this->validateSchemeField($data['vop_id_match'], 'vop_id_match', ['MTCH', 'NMTC', 'NOAP']);
                $response->setVopIdMatch($data['vop_id_match']);
            }

            if (isset($data['vop_name_match'])) {
                $this->validateSchemeField($data['vop_name_match'], 'vop_name_match', ['MTCH', 'NMTC', 'CMTC', 'NOAP']);
                $response->setVopNameMatch($data['vop_name_match']);
            }

            if (isset($data['cop_matched'])) {
                $copMatched = $this->validateAndNormalizeCopMatched($data['cop_matched']);
                $response->setCopMatched($copMatched);
            }

            if (isset($data['cop_reason'])) {
                $this->validateSchemeField($data['cop_reason'], 'cop_reason', ['MATC', 'ANNM', 'MBAM', 'BANM', 'PANM', 'BAMM', 'PAMM', 'AC01', 'IVCR', 'ACNS', 'OPTO', 'CASS', 'SCNS']);
                $response->setCopReason($data['cop_reason']);
            }

            if (isset($data['reason_code'])) {
                $response->setReasonCode($data['reason_code']);
            }
        }

        return $response;
    }

    private function validateResponseStructure(array $responseData): void
    {
        if (!isset($responseData['response_code'])) {
            throw new \InvalidArgumentException('Missing required field: response_code');
        }

        if (!isset($responseData['response_message'])) {
            throw new \InvalidArgumentException('Missing required field: response_message');
        }
    }

    private function validateResponseCode(string $responseCode): void
    {
        if (!preg_match('/^[0-9]{4}$/', $responseCode)) {
            throw new \InvalidArgumentException(sprintf('Invalid response_code format: %s', $responseCode));
        }

        $validCodes = [
            '2000', '2001', '2002', '2100', '2101', '2102', '2103', '2104', '2105', '2106',
            '4001', '4002', '4003', '4004', '4005', '4006',
            '5000', '5001', '5002'
        ];

        if (!in_array($responseCode, $validCodes, true)) {
            throw new \InvalidArgumentException(sprintf('Unknown response_code: %s', $responseCode));
        }
    }

    private function validateResponseMessage(string $responseMessage): void
    {
        if (strlen($responseMessage) > 255) {
            throw new \InvalidArgumentException('Response message too long (max 255 characters)');
        }

        if (empty(trim($responseMessage))) {
            throw new \InvalidArgumentException('Response message cannot be empty');
        }
    }

    private function validateDataSection(array $data): void
    {
        foreach ($data as $key => $value) {
            if (!is_scalar($value) && !is_array($value) && $value !== null) {
                throw new \InvalidArgumentException(sprintf('Invalid data type for field %s', $key));
            }
        }
    }

    private function validateAndNormalizeMatchScore($matchScore): ?float
    {
        if ($matchScore === null) {
            return null;
        }

        $score = (float) $matchScore;

        if ($score < 0.0 || $score > 1.0) {
            throw new \InvalidArgumentException(sprintf('Match score must be between 0.0 and 1.0, got: %f', $score));
        }

        return $score;
    }

    private function validateAndNormalizeLogicScore($logicScore): ?int
    {
        if ($logicScore === null) {
            return null;
        }

        $score = (int) $logicScore;

        if ($score < 0 || $score > 100) {
            throw new \InvalidArgumentException(sprintf('Logic score must be between 0 and 100, got: %d', $score));
        }

        return $score;
    }

    private function validateAndNormalizeCopMatched($copMatched): bool
    {
        if (is_bool($copMatched)) {
            return $copMatched;
        }

        if (is_string($copMatched)) {
            if ($copMatched === 'true') {
                return true;
            }
            if ($copMatched === 'false') {
                return false;
            }
        }

        throw new \InvalidArgumentException(sprintf('Invalid cop_matched value: %s', json_encode($copMatched)));
    }

    private function validateSchemeField($value, string $fieldName, array $allowedValues): void
    {
        if (!is_string($value)) {
            throw new \InvalidArgumentException(sprintf('%s must be a string, got: %s', $fieldName, gettype($value)));
        }

        if (!in_array($value, $allowedValues, true)) {
            throw new \InvalidArgumentException(sprintf('Invalid %s value: %s. Allowed values: %s', $fieldName, $value, implode(', ', $allowedValues)));
        }
    }
}
