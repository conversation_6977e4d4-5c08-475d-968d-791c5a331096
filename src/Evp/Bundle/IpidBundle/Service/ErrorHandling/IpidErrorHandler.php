<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service\ErrorHandling;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Service\IpidApiClient;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;
use Psr\Log\LoggerInterface;

class IpidErrorHandler
{
    private LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function handleApiException(IpidApiException $exception, TransferOut $transfer): IpidValidationResult
    {
        $this->logger->error('iPiD API error during validation', [
            'transfer_id' => $transfer->getId(),
            'error' => $exception->getMessage(),
            'error_code' => $exception->getErrorCode()
        ]);

        if ($this->isRetryableError($exception)) {
            return IpidValidationResult::createServerError($exception->getMessage(), $exception->getErrorCode());
        }

        return IpidValidationResult::createClientError($exception->getMessage(), $exception->getErrorCode());
    }

    public function handleGenericException(\Exception $exception, TransferOut $transfer): IpidValidationResult
    {
        $this->logger->error('Unexpected error during iPiD validation', [
            'transfer_id' => $transfer->getId(),
            'error' => $exception->getMessage()
        ]);

        return IpidValidationResult::createError('Internal validation error');
    }

    private function isRetryableError(IpidApiException $exception): bool
    {
        $retryableCodes = [
            IpidApiClient::ERROR_CODE_SERVICE_UNAVAILABLE,
            IpidApiClient::ERROR_CODE_TIMEOUT,
            IpidApiClient::ERROR_CODE_RATE_LIMIT_EXCEEDED
        ];

        return in_array($exception->getErrorCode(), $retryableCodes, true);
    }
}
