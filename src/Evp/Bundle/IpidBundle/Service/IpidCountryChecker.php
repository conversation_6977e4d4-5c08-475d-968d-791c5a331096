<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

class IpidCountryChecker
{
    private IpidCountryRegistry $countryRegistry;

    public function __construct(IpidCountryRegistry $countryRegistry)
    {
        $this->countryRegistry = $countryRegistry;
    }

    public function isCountryEnabled(string $countryCode): bool
    {
        return $this->countryRegistry->isCountryEnabled($countryCode);
    }

    public function isRegionEnabled(string $region): bool
    {
        return $this->countryRegistry->isRegionEnabled($region);
    }

    public function getEnabledCountries(): array
    {
        return $this->countryRegistry->getEnabledCountries();
    }

    public function getEnabledRegions(): array
    {
        return $this->countryRegistry->getEnabledRegions();
    }

    public function getValidationSchemeForCountry(string $countryCode): string
    {
        return $this->countryRegistry->getValidationSchemeForCountry($countryCode);
    }

    public function getRegionForCountry(string $countryCode): ?string
    {
        return $this->countryRegistry->getRegionForCountry($countryCode);
    }

    public function isVopCountry(string $countryCode): bool
    {
        return $this->getValidationSchemeForCountry($countryCode) === 'VOP';
    }

    public function isCopCountry(string $countryCode): bool
    {
        return $this->getValidationSchemeForCountry($countryCode) === 'COP';
    }

    public function supportsValidation(string $countryCode): bool
    {
        // Country must be enabled AND supported by corridor specification
        return $this->isCountryEnabled($countryCode);
    }
} 