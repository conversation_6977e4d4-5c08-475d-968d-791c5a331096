<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service\Processing;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\DTO\IpidValidationResponse;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;
use Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository;
use Evp\Bundle\IpidBundle\Repository\TransferIpidConsentRepository;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;

class IpidResponseProcessor
{
    private IpidApiResponseRepository $apiResponseRepository;
    private TransferIpidConsentRepository $consentRepository;

    public function __construct(
        IpidApiResponseRepository $apiResponseRepository,
        TransferIpidConsentRepository $consentRepository
    ) {
        $this->apiResponseRepository = $apiResponseRepository;
        $this->consentRepository = $consentRepository;
    }

    public function saveApiResponse(IpidValidationResponse $response, string $requestHash, string $country): IpidApiResponse
    {
        $ipidApiResponse = new IpidApiResponse();
        $ipidApiResponse->setRequestHash($requestHash);
        $ipidApiResponse->setCountry($country);
        $ipidApiResponse->setResponseStatus($response->getStatus());
        $ipidApiResponse->setResponseCode($response->getResponseCode());
        $ipidApiResponse->setResponseMessage($response->getResponseMessage());

        if ($response->getMatchScore() !== null) {
            $ipidApiResponse->setMatchScore($response->getMatchScore());
            $ipidApiResponse->setMatchLevel($this->determineMatchLevel($response->getMatchScore()));
        }

        $ipidApiResponse->setVopIdMatch($response->getVopIdMatch());
        $ipidApiResponse->setVopNameMatch($response->getVopNameMatch());
        $ipidApiResponse->setCopMatched($response->getCopMatched());
        $ipidApiResponse->setCopReason($response->getCopReason());
        $ipidApiResponse->setReasonCode($response->getReasonCode());

        return $this->apiResponseRepository->save($ipidApiResponse);
    }

    public function processValidationResult(
        TransferOut $transfer,
        IpidApiResponse $ipidApiResponse
    ): IpidValidationResult {
        $matchLevel = $ipidApiResponse->getMatchLevel() ?? TransferIpidConsent::MATCH_LEVEL_NO_MATCH;
        $consentStatus = $this->determineConsentStatus($ipidApiResponse);
        $consent = $this->createOrUpdateConsent($transfer, $ipidApiResponse, $consentStatus, $matchLevel);

        return IpidValidationResult::createSuccess($consent, $ipidApiResponse);
    }

    private function determineMatchLevel(float $matchScore): string
    {
        if ($matchScore >= 0.8) {
            return TransferIpidConsent::MATCH_LEVEL_STRONG;
        }

        if ($matchScore >= 0.5) {
            return TransferIpidConsent::MATCH_LEVEL_PARTIAL;
        }

        if ($matchScore > 0) {
            return TransferIpidConsent::MATCH_LEVEL_WEAK;
        }

        return TransferIpidConsent::MATCH_LEVEL_NO_MATCH;
    }

    private function determineConsentStatus(IpidApiResponse $ipidApiResponse): string
    {
        $responseCode = $ipidApiResponse->getResponseCode();

        if (str_starts_with($responseCode, '2')) {
            $matchLevel = $ipidApiResponse->getMatchLevel();
            
            if ($matchLevel === TransferIpidConsent::MATCH_LEVEL_STRONG) {
                return TransferIpidConsent::CONSENT_STATUS_GRANTED;
            }

            if (in_array($matchLevel, [TransferIpidConsent::MATCH_LEVEL_PARTIAL, TransferIpidConsent::MATCH_LEVEL_WEAK], true)) {
                return TransferIpidConsent::CONSENT_STATUS_REQUIRED;
            }
        }

        return TransferIpidConsent::CONSENT_STATUS_DENIED;
    }

    private function createOrUpdateConsent(
        TransferOut $transfer,
        IpidApiResponse $ipidApiResponse,
        string $consentStatus,
        string $matchLevel
    ): TransferIpidConsent {
        $consent = new TransferIpidConsent();
        $consent->setTransferId($transfer->getId());
        $consent->setIpidApiResponse($ipidApiResponse);
        $consent->setConsentStatus($consentStatus);
        $consent->setMatchLevel($matchLevel);

        $requiresReview = $this->shouldRequireManualReview($ipidApiResponse, $matchLevel);
        $consent->setRequiresManualReview($requiresReview);

        $validationNotes = $this->generateValidationNotes($ipidApiResponse);
        if ($validationNotes) {
            $consent->setValidationNotes($validationNotes);
        }

        return $this->consentRepository->save($consent);
    }

    private function shouldRequireManualReview(IpidApiResponse $ipidApiResponse, string $matchLevel): bool
    {
        if (in_array($matchLevel, [TransferIpidConsent::MATCH_LEVEL_PARTIAL, TransferIpidConsent::MATCH_LEVEL_WEAK], true)) {
            return true;
        }

        $responseCode = $ipidApiResponse->getResponseCode();
        $reviewRequiredCodes = ['2101', '2104', '4001', '4002'];

        return in_array($responseCode, $reviewRequiredCodes, true);
    }

    private function generateValidationNotes(IpidApiResponse $response): ?string
    {
        $notes = [];

        if ($response->getVopIdMatch()) {
            $sanitizedVopId = htmlspecialchars($response->getVopIdMatch(), ENT_QUOTES, 'UTF-8');
            $notes[] = sprintf('VOP ID match: %s', $sanitizedVopId);
        }

        if ($response->getVopNameMatch()) {
            $sanitizedVopName = htmlspecialchars($response->getVopNameMatch(), ENT_QUOTES, 'UTF-8');
            $notes[] = sprintf('VOP name match: %s', $sanitizedVopName);
        }

        return empty($notes) ? null : implode('; ', $notes);
    }
}
