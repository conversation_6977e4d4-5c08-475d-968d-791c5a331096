<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Service\Cache\IpidCacheManager;
use Evp\Bundle\IpidBundle\Service\ErrorHandling\IpidErrorHandler;
use Evp\Bundle\IpidBundle\Service\Processing\IpidResponseProcessor;
use Evp\Bundle\IpidBundle\Service\Validation\IpidTransferValidator;
use Psr\Log\LoggerInterface;

class IpidValidationService
{
    private IpidTransferValidator $transferValidator;
    private IpidRequestBuilder $requestBuilder;
    private IpidCacheManager $cacheManager;
    private IpidApiClient $apiClient;
    private IpidResponseProcessor $responseProcessor;
    private IpidErrorHandler $errorHandler;
    private IpidTransferHelper $transferHelper;
    private IpidCountryChecker $countryChecker;
    private LoggerInterface $logger;

    public function __construct(
        IpidTransferValidator $transferValidator,
        IpidRequestBuilder $requestBuilder,
        IpidCacheManager $cacheManager,
        IpidApiClient $apiClient,
        IpidResponseProcessor $responseProcessor,
        IpidErrorHandler $errorHandler,
        IpidTransferHelper $transferHelper,
        IpidCountryChecker $countryChecker,
        LoggerInterface $logger
    ) {
        $this->transferValidator = $transferValidator;
        $this->requestBuilder = $requestBuilder;
        $this->cacheManager = $cacheManager;
        $this->apiClient = $apiClient;
        $this->responseProcessor = $responseProcessor;
        $this->errorHandler = $errorHandler;
        $this->transferHelper = $transferHelper;
        $this->countryChecker = $countryChecker;
        $this->logger = $logger;
    }

    /**
     * Validate transfer according to EU iPiD requirements.
     *
     * Implements the complete workflow:
     * 1. Check if it's transfer to EU
     * 2. Check if validation was already done
     * 3. Build check request
     * 4. Check for cached response (30-day TTL)
     * 5. Send API request if needed
     * 6. Save response entity
     * 7. Link transfer to response via consent
     *
     * Never throws errors - creates consent for unsupported countries.
     */
    public function validateTransfer(TransferOut $transfer): IpidValidationResult
    {
        try {
            // Step 1: Check if it's transfer to EU
            $country = $this->transferHelper->extractCountryFromTransfer($transfer);

            if (!$this->isEuTransfer($country)) {
                $this->logger->debug('iPiD validation skipped - not an EU transfer', [
                    'transfer_id' => $transfer->getId(),
                    'country' => $country
                ]);
                return $this->createUnsupportedCountryConsent($transfer, $country);
            }

            $this->logger->info('iPiD validation initiated for EU transfer', [
                'transfer_id' => $transfer->getId(),
                'country' => $country,
                'validation_scheme' => $this->countryChecker->getValidationSchemeForCountry($country),
                'region' => $this->countryChecker->getRegionForCountry($country)
            ]);

            // Step 2: Check if validation was already done
            $existingConsent = $this->transferValidator->findExistingConsent($transfer);
            if ($existingConsent) {
                $this->logger->info('iPiD validation already completed for transfer', [
                    'transfer_id' => $transfer->getId(),
                    'consent_id' => $existingConsent->getId(),
                    'consent_status' => $existingConsent->getConsentStatus()
                ]);
                return IpidValidationResult::createAlreadyValidated($existingConsent);
            }

            // Step 3: Build check request
            $request = $this->requestBuilder->buildRequest($transfer);
            $requestHash = $this->cacheManager->calculateRequestHash($request, $transfer);

            // Step 4: Check for cached response (30-day TTL)
            $cachedResponse = $this->cacheManager->findValidCachedResponse($requestHash, $country);
            if ($cachedResponse) {
                $this->logger->info('Using cached iPiD response for EU transfer', [
                    'transfer_id' => $transfer->getId(),
                    'request_hash' => $requestHash,
                    'cached_response_age' => $cachedResponse->getCreatedAt()->diff(new \DateTime())->days . ' days'
                ]);
                // Step 7: Link transfer to cached response via consent
                return $this->responseProcessor->processValidationResult($transfer, $cachedResponse);
            }

            // Step 5: Send API request
            $response = $this->apiClient->sendValidationRequest($request);

            // Step 6: Save response entity for reuse
            $ipidApiResponse = $this->responseProcessor->saveApiResponse($response, $requestHash, $country);

            // Step 7: Link transfer to response via consent
            return $this->responseProcessor->processValidationResult($transfer, $ipidApiResponse);

        } catch (IpidApiException $e) {
            // Handle API errors gracefully - create consent without manual approval
            $this->logger->warning('iPiD API error - creating automatic consent', [
                'transfer_id' => $transfer->getId(),
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage()
            ]);
            return $this->createErrorConsent($transfer, $country, $e->getMessage());
        } catch (\Exception $e) {
            // Handle any other errors gracefully - create consent without manual approval
            $this->logger->error('iPiD validation error - creating automatic consent', [
                'transfer_id' => $transfer->getId(),
                'error_class' => get_class($e),
                'error_message' => $e->getMessage()
            ]);
            return $this->createErrorConsent($transfer, $country, $e->getMessage());
        }
    }

    /**
     * Check if the transfer is to an EU country that requires iPiD validation.
     */
    private function isEuTransfer(string $country): bool
    {
        // Check if country is enabled for iPiD validation
        if (!$this->countryChecker->isCountryEnabled($country)) {
            return false;
        }

        // Check if it's an EU region or specific EU countries
        $region = $this->countryChecker->getRegionForCountry($country);
        if ($region === 'EU') {
            return true;
        }

        // Include UK as it may still require validation
        if ($region === 'UK') {
            return true;
        }

        // Check for specific EU countries that might not be in EU region
        $euCountries = ['AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE'];
        return in_array($country, $euCountries, true);
    }

    /**
     * Create consent for unsupported countries without requiring manual approval.
     */
    private function createUnsupportedCountryConsent(TransferOut $transfer, string $country): IpidValidationResult
    {
        $this->logger->info('Creating automatic consent for unsupported country', [
            'transfer_id' => $transfer->getId(),
            'country' => $country
        ]);

        // Create a consent that allows the transfer to proceed
        $consent = $this->responseProcessor->createAutomaticConsent(
            $transfer,
            'unsupported_country',
            'Country not configured for iPiD validation'
        );

        return IpidValidationResult::createUnsupportedCountry($country, $consent);
    }

    /**
     * Create consent for errors without requiring manual approval.
     */
    private function createErrorConsent(TransferOut $transfer, string $country, string $errorMessage): IpidValidationResult
    {
        $this->logger->info('Creating automatic consent due to validation error', [
            'transfer_id' => $transfer->getId(),
            'country' => $country,
            'error_message' => $errorMessage
        ]);

        // Create a consent that allows the transfer to proceed
        $consent = $this->responseProcessor->createAutomaticConsent(
            $transfer,
            'validation_error',
            $errorMessage
        );

        return IpidValidationResult::createError($errorMessage, $consent);
    }
}
