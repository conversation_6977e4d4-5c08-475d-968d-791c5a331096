<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;

class IpidValidationService
{
    private IpidValidationOrchestrator $orchestrator;

    public function __construct(IpidValidationOrchestrator $orchestrator)
    {
        $this->orchestrator = $orchestrator;
    }

    public function validateTransfer(TransferOut $transfer): IpidValidationResult
    {
        return $this->orchestrator->validateTransfer($transfer);
    }
}
