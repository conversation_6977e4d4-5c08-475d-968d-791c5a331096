<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\DTO\IpidValidationResponse;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;
use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository;
use Evp\Bundle\IpidBundle\Repository\TransferIpidConsentRepository;
use Psr\Log\LoggerInterface;

class IpidValidationService
{
    private IpidApiClient $apiClient;
    private IpidRequestBuilder $requestBuilder;
    private IpidApiResponseRepository $ipidApiResponseRepository;
    private TransferIpidConsentRepository $transferIpidConsentRepository;
    private IpidTransferHelper $transferHelper;
    private IpidCorridorSpecification $corridorSpecification;
    private IpidCountryChecker $countryChecker;
    private LoggerInterface $logger;

    public function __construct(
        IpidApiClient $apiClient,
        IpidRequestBuilder $requestBuilder,
        IpidApiResponseRepository $ipidApiResponseRepository,
        TransferIpidConsentRepository $transferIpidConsentRepository,
        IpidTransferHelper $transferHelper,
        IpidCorridorSpecification $corridorSpecification,
        IpidCountryChecker $countryChecker,
        LoggerInterface $logger
    ) {
        $this->apiClient = $apiClient;
        $this->requestBuilder = $requestBuilder;
        $this->ipidApiResponseRepository = $ipidApiResponseRepository;
        $this->transferIpidConsentRepository = $transferIpidConsentRepository;
        $this->transferHelper = $transferHelper;
        $this->corridorSpecification = $corridorSpecification;
        $this->countryChecker = $countryChecker;
        $this->logger = $logger;
    }

    public function validateTransfer(TransferOut $transfer): IpidValidationResult
    {
        try {
            $country = $this->transferHelper->extractCountryFromTransfer($transfer);

            if (!$this->isCountrySupported($country)) {
                $enabledCountries = $this->countryChecker->getEnabledCountries();
                $enabledRegions = $this->countryChecker->getEnabledRegions();

                $this->logger->warning('iPiD validation attempted for unsupported country', [
                    'transfer_id' => $transfer->getId(),
                    'country' => $country,
                    'enabled_countries' => $enabledCountries,
                    'enabled_regions' => $enabledRegions,
                    'validation_scheme' => $this->countryChecker->getValidationSchemeForCountry($country),
                    'country_region' => $this->countryChecker->getRegionForCountry($country)
                ]);
                return IpidValidationResult::createUnsupportedCountry($country);
            }

            // Log successful country validation with scheme info
            $this->logger->info('iPiD validation initiated for supported country', [
                'transfer_id' => $transfer->getId(),
                'country' => $country,
                'validation_scheme' => $this->countryChecker->getValidationSchemeForCountry($country),
                'country_region' => $this->countryChecker->getRegionForCountry($country)
            ]);

            $existingConsent = $this->transferIpidConsentRepository->findByTransferId($transfer->getId());
            if ($existingConsent) {
                return IpidValidationResult::createAlreadyValidated($existingConsent);
            }

            $request = $this->requestBuilder->buildRequest($transfer);

            $requestHash = $this->calculateRequestHash($request, $transfer);

            $cachedResponse = $this->findValidCachedResponse($requestHash, $country);
            if ($cachedResponse) {
                $this->logger->info('Using cached iPiD response', [
                    'transfer_id' => $transfer->getId(),
                    'request_hash' => $requestHash
                ]);
                return $this->processValidationResult($transfer, $cachedResponse, $request);
            }

            $response = $this->apiClient->sendValidationRequest($request);
            $ipidApiResponse = $this->saveApiResponse($response, $requestHash, $country);

            return $this->processValidationResult($transfer, $ipidApiResponse, $request);

        } catch (IpidApiException $e) {
            $this->logger->error('iPiD API error during validation', [
                'transfer_id' => $transfer->getId(),
                'error' => $e->getMessage(),
                'error_code' => $e->getErrorCode()
            ]);

            return IpidValidationResult::createError($e->getMessage());
        } catch (\Exception $e) {
            $this->logger->error('Unexpected error during iPiD validation', [
                'transfer_id' => $transfer->getId(),
                'error' => $e->getMessage()
            ]);

            return IpidValidationResult::createError('Internal validation error');
        }
    }

    private function calculateRequestHash(IpidValidationRequest $request, TransferOut $transfer): string
    {
        $data = [
            'node_id' => $request->getNodeId(),
            'account_number' => $this->transferHelper->getBeneficiaryAccount($transfer),
            'beneficiary_name' => $this->transferHelper->getBeneficiaryName($transfer),
            'country' => $this->transferHelper->extractCountryFromTransfer($transfer),
            'clearing_system_id' => $request->getClearingSystemId(),
            'bic' => $request->getBic(),
        ];

        $personCode = $this->transferHelper->extractPersonCodeFromTransfer($transfer);
        if ($personCode) {
            $data['person_code'] = $personCode;
        }

        ksort($data);

        $hashInput = json_encode($data, JSON_THROW_ON_ERROR);
        $hash = hash('sha256', $hashInput);

        // Add entropy check - if hash looks suspicious, log it
        if (substr_count($hash, '0') > 20 || substr_count($hash, 'f') > 20) {
            $this->logger->warning('Potentially weak hash generated for iPiD request', [
                'transfer_id' => $transfer->getId(),
                'hash' => $hash,
                'hash_input_length' => strlen($hashInput)
            ]);
        }

        return $hash;
    }

    private function findValidCachedResponse(string $requestHash, string $country): ?IpidApiResponse
    {
        $response = $this->ipidApiResponseRepository->findValidByRequestHashAndCountry($requestHash, $country);

        return ($response && !$response->isExpired()) ? $response : null;
    }

    private function saveApiResponse(
        IpidValidationResponse $response,
        string $requestHash,
        string $country
    ): IpidApiResponse {
        $apiResponse = new IpidApiResponse();
        $apiResponse->setRequestHash($requestHash);
        $apiResponse->setCountry($country);
        $apiResponse->setMatchScore($response->getMatchScore());
        $apiResponse->setMatchLevel($response->getMatchLevel());
        $apiResponse->setResponseStatus($response->getStatus());
        $apiResponse->setResponseCode($response->getResponseCode());
        $apiResponse->setResponseMessage($response->getResponseMessage());
        $apiResponse->setRequiresConsent($response->getRequiresConsent());

        // Store scheme-specific response fields for audit and compliance
        $apiResponse->setVopIdMatch($response->getVopIdMatch());
        $apiResponse->setVopNameMatch($response->getVopNameMatch());
        $apiResponse->setCopMatched($response->getCopMatched());
        $apiResponse->setCopReason($response->getCopReason());
        $apiResponse->setReasonCode($response->getReasonCode());

        return $this->ipidApiResponseRepository->save($apiResponse);
    }

    private function processValidationResult(
        TransferOut $transfer,
        IpidApiResponse $ipidApiResponse,
        IpidValidationRequest $request
    ): IpidValidationResult {
        // Use match level and score directly from API response
        $matchLevel = $ipidApiResponse->getMatchLevel() ?? TransferIpidConsent::MATCH_LEVEL_NO_MATCH;

        $consentStatus = $this->determineConsentStatus($ipidApiResponse);

        $consent = $this->createOrUpdateConsent($transfer, $ipidApiResponse, $consentStatus, $matchLevel);

        return IpidValidationResult::createSuccess($consent, $ipidApiResponse);
    }

    private function determineConsentStatus(IpidApiResponse $ipidApiResponse): string
    {
        // Use API response to determine consent status
        if (!$ipidApiResponse->isSuccessful()) {
            return TransferIpidConsent::CONSENT_STATUS_DENIED;
        }

        if ($ipidApiResponse->getRequiresConsent()) {
            return TransferIpidConsent::CONSENT_STATUS_REQUIRED;
        }

        // Use match level from API response directly
        $matchLevel = $ipidApiResponse->getMatchLevel();

        switch ($matchLevel) {
            case TransferIpidConsent::MATCH_LEVEL_STRONG:
                return TransferIpidConsent::CONSENT_STATUS_GRANTED;
            case TransferIpidConsent::MATCH_LEVEL_PARTIAL:
            case TransferIpidConsent::MATCH_LEVEL_WEAK:
                return TransferIpidConsent::CONSENT_STATUS_PENDING;
            default:
                return TransferIpidConsent::CONSENT_STATUS_DENIED;
        }
    }

    private function createOrUpdateConsent(
        TransferOut $transfer,
        IpidApiResponse $ipidApiResponse,
        string $consentStatus,
        string $matchLevel
    ): TransferIpidConsent {
        $consent = new TransferIpidConsent();
        $consent->setTransferId($transfer->getId());
        $consent->setIpidApiResponse($ipidApiResponse);
        $consent->setConsentStatus($consentStatus);
        $consent->setMatchLevel($matchLevel);

        $requiresManualReview = $this->shouldRequireManualReview($matchLevel, $ipidApiResponse);
        $consent->setRequiresManualReview($requiresManualReview);

        if ($requiresManualReview) {
            $notes = $this->generateValidationNotes($ipidApiResponse);
            $consent->setValidationNotes($notes);
        }

        return $this->transferIpidConsentRepository->save($consent);
    }

    private function shouldRequireManualReview(string $matchLevel, IpidApiResponse $response): bool
    {
        // Review required for partial/weak matches or any response that's not successful but not an outright failure
        return $matchLevel === TransferIpidConsent::MATCH_LEVEL_PARTIAL ||
               $matchLevel === TransferIpidConsent::MATCH_LEVEL_WEAK ||
               ($matchLevel === TransferIpidConsent::MATCH_LEVEL_STRONG && !$response->isSuccessful());
    }

    private function generateValidationNotes(IpidApiResponse $response): string
    {
        $notes = [];

        if (!$response->isSuccessful()) {
            $sanitizedMessage = htmlspecialchars($response->getResponseMessage(), ENT_QUOTES, 'UTF-8');
            $notes[] = sprintf('API validation failed: %s', $sanitizedMessage);
        }

        $matchLevel = $response->getMatchLevel();
        if ($matchLevel === TransferIpidConsent::MATCH_LEVEL_PARTIAL) {
            $notes[] = 'Partial match confidence - requires review';
        } elseif ($matchLevel === TransferIpidConsent::MATCH_LEVEL_WEAK) {
            $notes[] = 'Weak match confidence - requires review';
        }

        if ($response->getRequiresConsent()) {
            $notes[] = 'Explicit consent required from customer';
        }

        $responseCode = $response->getResponseCode();
        if (in_array($responseCode, ['2101', '2104'], true)) {
            $sanitizedCode = preg_replace('/[^0-9]/', '', $responseCode);
            $notes[] = sprintf('Manual verification recommended based on response code: %s', $sanitizedCode);
        }

        // Add scheme-specific validation notes
        if ($response->getCopReason()) {
            $sanitizedReason = htmlspecialchars($response->getCopReason(), ENT_QUOTES, 'UTF-8');
            $notes[] = sprintf('COP reason: %s', $sanitizedReason);
        }

        if ($response->getVopIdMatch()) {
            $sanitizedVopId = htmlspecialchars($response->getVopIdMatch(), ENT_QUOTES, 'UTF-8');
            $notes[] = sprintf('VOP ID match: %s', $sanitizedVopId);
        }

        if ($response->getVopNameMatch()) {
            $sanitizedVopName = htmlspecialchars($response->getVopNameMatch(), ENT_QUOTES, 'UTF-8');
            $notes[] = sprintf('VOP name match: %s', $sanitizedVopName);
        }

        return implode('; ', $notes);
    }

    private function isCountrySupported(string $country): bool
    {
        // Use the configurable country checker instead of hardcoded corridor specification
        $isEnabled = $this->countryChecker->supportsValidation($country);

        // Additional check: ensure the corridor specification also supports this country
        // This provides a safety check that the country has proper API integration
        $hasCorridorSupport = $this->corridorSpecification->isCountrySupported($country);

        return $isEnabled && $hasCorridorSupport;
    }
}
