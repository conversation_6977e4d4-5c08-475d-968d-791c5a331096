<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

interface IpidEncryptionService
{
    /**
     * Encrypts the payload using the provided public key
     */
    public function encrypt(string $payload, string $publicKey = null): string;

    /**
     * Decrypts the response payload using the private key
     */
    public function decrypt(string $encryptedPayload): string;
} 