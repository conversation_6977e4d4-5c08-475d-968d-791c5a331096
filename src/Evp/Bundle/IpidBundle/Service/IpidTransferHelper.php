<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;

class IpidTransferHelper
{

    public function extractCountryFromTransfer(TransferOut $transfer): string
    {
        $beneficiary = $transfer->getBeneficiary();

        // Try to get country from IBAN if it's a PartyIban
        if ($beneficiary instanceof PartyIban) {
            $iban = $beneficiary->getIban();
            if ($iban && strlen($iban) >= 2) {
                return strtoupper(substr($iban, 0, 2));
            }
        }

        // For non-IBAN accounts, try to determine country from other fields
        if ($beneficiary instanceof PartyBank) {
            // Try BIC code to determine country (first 2 chars typically indicate country)
            $bic = $beneficiary->getBic();
            if ($bic && strlen($bic) >= 6) {
                $countryFromBic = strtoupper(substr($bic, 4, 2));
                // Validate it looks like a country code
                if (preg_match('/^[A-Z]{2}$/', $countryFromBic)) {
                    return $countryFromBic;
                }
            }
        }

        throw new \InvalidArgumentException('Cannot determine country from transfer beneficiary data.');
    }

    public function getBeneficiaryAccount(TransferOut $transfer): ?string
    {
        $beneficiary = $transfer->getBeneficiary();

        if (!$beneficiary) {
            return null;
        }

        // For IBAN parties, get IBAN
        if ($beneficiary instanceof PartyIban) {
            return $beneficiary->getIban();
        }

        // For other bank parties, try to get account from display method
        if ($beneficiary instanceof PartyBank) {
            return $beneficiary->getDisplayAccount();
        }

        return null;
    }

    public function getBeneficiaryName(TransferOut $transfer): ?string
    {
        $beneficiary = $transfer->getBeneficiary();

        if (!$beneficiary) {
            return null;
        }

        if (method_exists($beneficiary, 'getName')) {
            return $beneficiary->getName();
        }

        if (method_exists($beneficiary, 'getDisplayName')) {
            return $beneficiary->getDisplayName();
        }

        return null;
    }

    public function getBeneficiaryBankBic(TransferOut $transfer): ?string
    {
        $beneficiary = $transfer->getBeneficiary();

        if ($beneficiary instanceof PartyBank) {
            return $beneficiary->getBic();
        }

        return null;
    }

    public function getBeneficiarySortCode(TransferOut $transfer): ?string
    {
        $beneficiary = $transfer->getBeneficiary();

        if ($beneficiary instanceof PartyBank) {
            return $beneficiary->getBankCode();
        }

        return null;
    }

    public function getBeneficiaryBankName(TransferOut $transfer): ?string
    {
        $beneficiary = $transfer->getBeneficiary();

        if ($beneficiary instanceof PartyBank) {
            return $beneficiary->getBankName();
        }

        return null;
    }

    public function extractPersonCodeFromTransfer(TransferOut $transfer): ?string
    {
        $beneficiary = $transfer->getBeneficiary();

        if (method_exists($beneficiary, 'getCode')) {
            $code = $beneficiary->getCode();
            if ($code) {
                return $code;
            }
        }

        if ($beneficiary instanceof PartyBank) {
            $innCode = $beneficiary->getInnCode();
            if ($innCode) {
                return $innCode;
            }

            $kppCode = $beneficiary->getKppCode();
            if ($kppCode) {
                return $kppCode;
            }
        }

        return null;
    }

    public function getCurrency(TransferOut $transfer): ?string
    {
        $amountMoney = $transfer->getAmountMoney();
        if ($amountMoney) {
            return $amountMoney->getCurrency();
        }

        // Fallback to currency field if money object is not available
        if (method_exists($transfer, 'getAmountCurrency')) {
            return $transfer->getAmountCurrency();
        }

        return null;
    }

    public function getBeneficiaryType(TransferOut $transfer): ?string
    {
        $beneficiary = $transfer->getBeneficiary();

        if (!$beneficiary) {
            return null;
        }

        if ($beneficiary instanceof PartyBank) {
            if ($beneficiary->getInnCode() || $beneficiary->getKppCode()) {
                return 'business';
            }
        }

        return 'individual';
    }

    public function generateRequestHash(array $requestData): string
    {
        ksort($requestData);
        return hash('sha256', json_encode($requestData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));
    }
}
