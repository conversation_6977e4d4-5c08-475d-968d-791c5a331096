<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service\Validation;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;
use Evp\Bundle\IpidBundle\Repository\TransferIpidConsentRepository;
use Evp\Bundle\IpidBundle\Service\IpidCorridorSpecification;
use Evp\Bundle\IpidBundle\Service\IpidCountryChecker;

class IpidTransferValidator
{
    private IpidCountryChecker $countryChecker;
    private TransferIpidConsentRepository $consentRepository;
    private IpidCorridorSpecification $corridorSpecification;

    public function __construct(
        IpidCountryChecker $countryChecker,
        TransferIpidConsentRepository $consentRepository,
        IpidCorridorSpecification $corridorSpecification
    ) {
        $this->countryChecker = $countryChecker;
        $this->consentRepository = $consentRepository;
        $this->corridorSpecification = $corridorSpecification;
    }

    public function isCountrySupported(string $country): bool
    {
        $isEnabled = $this->countryChecker->supportsValidation($country);
        $hasCorridorSupport = $this->corridorSpecification->isCountrySupported($country);

        return $isEnabled && $hasCorridorSupport;
    }

    public function hasExistingConsent(TransferOut $transfer): bool
    {
        $existingConsent = $this->consentRepository->findByTransferId($transfer->getId());

        return $existingConsent !== null;
    }

    public function getExistingConsent(TransferOut $transfer): ?TransferIpidConsent
    {
        return $this->consentRepository->findByTransferId($transfer->getId());
    }
}
