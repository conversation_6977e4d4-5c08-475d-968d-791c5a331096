<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service\Validation;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\Contract\CountryValidatorInterface;
use Evp\Bundle\IpidBundle\Contract\TransferValidatorInterface;
use Evp\Bundle\IpidBundle\Repository\TransferIpidConsentRepository;
use Evp\Bundle\IpidBundle\Service\IpidCorridorSpecification;
use Evp\Bundle\IpidBundle\Service\IpidTransferHelper;

class TransferValidator implements TransferValidatorInterface
{
    private CountryValidatorInterface $countryValidator;
    private TransferIpidConsentRepository $consentRepository;
    private IpidTransferHelper $transferHelper;
    private IpidCorridorSpecification $corridorSpecification;

    public function __construct(
        CountryValidatorInterface $countryValidator,
        TransferIpidConsentRepository $consentRepository,
        IpidTransferHelper $transferHelper,
        IpidCorridorSpecification $corridorSpecification
    ) {
        $this->countryValidator = $countryValidator;
        $this->consentRepository = $consentRepository;
        $this->transferHelper = $transferHelper;
        $this->corridorSpecification = $corridorSpecification;
    }

    public function isCountrySupported(string $country): bool
    {
        $isEnabled = $this->countryValidator->supportsValidation($country);
        $hasCorridorSupport = $this->corridorSpecification->isCountrySupported($country);

        return $isEnabled && $hasCorridorSupport;
    }

    public function hasExistingConsent(TransferOut $transfer): bool
    {
        $existingConsent = $this->consentRepository->findByTransferId($transfer->getId());
        
        return $existingConsent !== null;
    }
}
