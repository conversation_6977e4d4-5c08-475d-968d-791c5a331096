<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Service\Cache\IpidCacheManager;
use Evp\Bundle\IpidBundle\Service\ErrorHandling\IpidErrorHandler;
use Evp\Bundle\IpidBundle\Service\Processing\IpidResponseProcessor;
use Evp\Bundle\IpidBundle\Service\Validation\IpidTransferValidator;
use Psr\Log\LoggerInterface;

class IpidValidationOrchestrator
{
    private IpidTransferValidator $transferValidator;
    private IpidRequestBuilder $requestBuilder;
    private IpidCacheManager $cacheManager;
    private IpidApiClient $apiClient;
    private IpidResponseProcessor $responseProcessor;
    private IpidErrorHandler $errorHandler;
    private IpidTransferHelper $transferHelper;
    private IpidCountryChecker $countryChecker;
    private LoggerInterface $logger;

    public function __construct(
        IpidTransferValidator $transferValidator,
        IpidRequestBuilder $requestBuilder,
        IpidCacheManager $cacheManager,
        IpidApiClient $apiClient,
        IpidResponseProcessor $responseProcessor,
        IpidErrorHandler $errorHandler,
        IpidTransferHelper $transferHelper,
        IpidCountryChecker $countryChecker,
        LoggerInterface $logger
    ) {
        $this->transferValidator = $transferValidator;
        $this->requestBuilder = $requestBuilder;
        $this->cacheManager = $cacheManager;
        $this->apiClient = $apiClient;
        $this->responseProcessor = $responseProcessor;
        $this->errorHandler = $errorHandler;
        $this->transferHelper = $transferHelper;
        $this->countryChecker = $countryChecker;
        $this->logger = $logger;
    }

    public function validateTransfer(TransferOut $transfer): IpidValidationResult
    {
        try {
            $country = $this->transferHelper->extractCountryFromTransfer($transfer);

            if (!$this->transferValidator->isCountrySupported($country)) {
                $this->logUnsupportedCountry($transfer, $country);
                return IpidValidationResult::createUnsupportedCountry($country);
            }

            $this->logSupportedCountry($transfer, $country);

            if ($this->transferValidator->hasExistingConsent($transfer)) {
                $existingConsent = $this->transferValidator->getExistingConsent($transfer);
                return IpidValidationResult::createAlreadyValidated($existingConsent);
            }

            $request = $this->requestBuilder->buildRequest($transfer);
            $requestHash = $this->cacheManager->calculateRequestHash($request, $transfer);

            $cachedResponse = $this->cacheManager->findValidCachedResponse($requestHash, $country);
            if ($cachedResponse) {
                $this->logger->info('Using cached iPiD response', [
                    'transfer_id' => $transfer->getId(),
                    'request_hash' => $requestHash
                ]);
                return $this->responseProcessor->processValidationResult($transfer, $cachedResponse);
            }

            $response = $this->apiClient->sendValidationRequest($request);
            $ipidApiResponse = $this->responseProcessor->saveApiResponse($response, $requestHash, $country);

            return $this->responseProcessor->processValidationResult($transfer, $ipidApiResponse);

        } catch (IpidApiException $e) {
            return $this->errorHandler->handleApiException($e, $transfer);
        } catch (\Exception $e) {
            return $this->errorHandler->handleGenericException($e, $transfer);
        }
    }

    private function logUnsupportedCountry(TransferOut $transfer, string $country): void
    {
        $enabledCountries = $this->countryChecker->getEnabledCountries();
        $enabledRegions = $this->countryChecker->getEnabledRegions();

        $this->logger->warning('iPiD validation attempted for unsupported country', [
            'transfer_id' => $transfer->getId(),
            'country' => $country,
            'enabled_countries' => $enabledCountries,
            'enabled_regions' => $enabledRegions,
            'validation_scheme' => $this->countryChecker->getValidationSchemeForCountry($country),
            'country_region' => $this->countryChecker->getRegionForCountry($country)
        ]);
    }

    private function logSupportedCountry(TransferOut $transfer, string $country): void
    {
        $this->logger->info('iPiD validation initiated for supported country', [
            'transfer_id' => $transfer->getId(),
            'country' => $country,
            'validation_scheme' => $this->countryChecker->getValidationSchemeForCountry($country),
            'country_region' => $this->countryChecker->getRegionForCountry($country)
        ]);
    }
}
