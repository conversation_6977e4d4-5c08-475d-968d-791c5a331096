<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;

class IpidValidationResult
{
    public const STATUS_SUCCESS = 'success';
    public const STATUS_ERROR = 'error';
    public const STATUS_SERVER_ERROR = 'server_error';
    public const STATUS_CLIENT_ERROR = 'client_error';
    public const STATUS_VALIDATION_FAILED = 'validation_failed';
    public const STATUS_UNSUPPORTED_COUNTRY = 'unsupported_country';
    public const STATUS_ALREADY_VALIDATED = 'already_validated';

    private string $status;
    private ?string $errorMessage;
    private ?string $errorCode;
    private ?TransferIpidConsent $consent;
    private ?IpidApiResponse $apiResponse;
    private bool $isRetryable;

    private function __construct(
        string $status,
        ?string $errorMessage = null,
        ?string $errorCode = null,
        ?TransferIpidConsent $consent = null,
        ?IpidApiResponse $apiResponse = null,
        bool $isRetryable = false
    ) {
        $this->status = $status;
        $this->errorMessage = $errorMessage;
        $this->errorCode = $errorCode;
        $this->consent = $consent;
        $this->apiResponse = $apiResponse;
        $this->isRetryable = $isRetryable;
    }

    public static function createSuccess(TransferIpidConsent $consent, IpidApiResponse $apiResponse): self
    {
        return new self(self::STATUS_SUCCESS, null, null, $consent, $apiResponse);
    }

    public static function createError(string $errorMessage): self
    {
        return new self(self::STATUS_ERROR, $errorMessage);
    }

    public static function createServerError(string $errorMessage, string $errorCode): self
    {
        return new self(self::STATUS_SERVER_ERROR, $errorMessage, $errorCode, null, null, true);
    }

    public static function createClientError(string $errorMessage, string $errorCode): self
    {
        return new self(self::STATUS_CLIENT_ERROR, $errorMessage, $errorCode, null, null, false);
    }

    public static function createValidationFailed(TransferIpidConsent $consent, IpidApiResponse $apiResponse): self
    {
        return new self(self::STATUS_VALIDATION_FAILED, null, $apiResponse->getResponseCode(), $consent, $apiResponse);
    }

    public static function createUnsupportedCountry(string $country): self
    {
        return new self(self::STATUS_UNSUPPORTED_COUNTRY, "Country $country is not supported for iPiD validation");
    }

    public static function createAlreadyValidated(TransferIpidConsent $consent): self
    {
        return new self(self::STATUS_ALREADY_VALIDATED, null, null, $consent);
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }

    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }

    public function getConsent(): ?TransferIpidConsent
    {
        return $this->consent;
    }

    public function getApiResponse(): ?IpidApiResponse
    {
        return $this->apiResponse;
    }

    public function isSuccessful(): bool
    {
        return $this->status === self::STATUS_SUCCESS;
    }

    public function isError(): bool
    {
        return in_array($this->status, [
            self::STATUS_ERROR,
            self::STATUS_SERVER_ERROR,
            self::STATUS_CLIENT_ERROR,
            self::STATUS_VALIDATION_FAILED
        ], true);
    }

    public function isServerError(): bool
    {
        return $this->status === self::STATUS_SERVER_ERROR;
    }

    public function isClientError(): bool
    {
        return $this->status === self::STATUS_CLIENT_ERROR;
    }

    public function isValidationFailed(): bool
    {
        return $this->status === self::STATUS_VALIDATION_FAILED;
    }

    public function isAlreadyValidated(): bool
    {
        return $this->status === self::STATUS_ALREADY_VALIDATED;
    }

    public function isUnsupportedCountry(): bool
    {
        return $this->status === self::STATUS_UNSUPPORTED_COUNTRY;
    }

    public function isRetryable(): bool
    {
        return $this->isRetryable;
    }

    public function canProceed(): bool
    {
        return $this->isSuccessful() || $this->isAlreadyValidated();
    }

    public function requiresManualReview(): bool
    {
        return $this->consent && $this->consent->requiresManualReview();
    }
} 