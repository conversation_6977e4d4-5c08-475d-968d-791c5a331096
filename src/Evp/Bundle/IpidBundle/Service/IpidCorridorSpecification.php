<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

class IpidCorridorSpecification
{
    // Based on node_api.txt section 11.6 Corridor Request and Response Specifications
    private const CORRIDOR_SPECS = [
        'US' => [
            'creditor' => ['given_name' => true, 'surname' => true], // US requires split names
            'creditor_identification' => ['registration_id' => false], // Optional
            'creditor_account' => ['account_id' => true], // Account ID required
            'creditor_agent' => ['clearing_system_id' => false], // Optional routing enhancement
            'account_type' => 'account_id', // Use account_id not IBAN
        ],
        'CN' => [
            'creditor' => ['name' => true], // Chinese characters preferred
            'creditor_identification' => ['registration_id' => false], // Optional
            'creditor_account' => ['account_id' => true], // Account ID in 62* PAN format
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'BD' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['bic' => false], // Optional BIC codes like MTBLBDDH
            'account_type' => 'account_id',
        ],
        'BR' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false], // CPF/CNPJ format optional
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'MX' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true], // CLABE format
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'AR' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true], // CBU format
            'creditor_agent' => ['bic' => false],
            'account_type' => 'account_id',
        ],
        'BE' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false], // VAT, Company No.
            'creditor_account' => ['iban' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'iban',
        ],
        'IT' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false], // VAT
            'creditor_account' => ['iban' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'iban',
        ],
        'UY' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'account_id',
        ],
        'PE' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true], // CCI format
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'MY' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'TR' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true, 'iban' => false], // Both supported
            'creditor_agent' => ['bic' => false],
            'account_type' => 'flexible', // Can use either
        ],
        'KR' => [
            'creditor' => ['name' => true], // Korean characters
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'account_id',
        ],
    ];

    public function getRequiredFields(string $countryCode): array
    {
        $countryCode = strtoupper($countryCode);

        if (!isset(self::CORRIDOR_SPECS[$countryCode])) {
            // Default specification for unsupported countries
            return [
                'creditor' => ['name' => true],
                'creditor_identification' => ['registration_id' => false],
                'creditor_account' => ['account_id' => true],
                'creditor_agent' => ['bic' => false],
                'account_type' => 'flexible',
            ];
        }

        return self::CORRIDOR_SPECS[$countryCode];
    }

    public function shouldSplitName(string $countryCode): bool
    {
        $spec = $this->getRequiredFields($countryCode);

        return isset($spec['creditor']['given_name']) && $spec['creditor']['given_name'];
    }

    public function getPreferredAccountType(string $countryCode): string
    {
        $spec = $this->getRequiredFields($countryCode);

        return $spec['account_type'] ?? 'flexible';
    }

    public function isRegistrationIdRequired(string $countryCode): bool
    {
        $spec = $this->getRequiredFields($countryCode);

        return $spec['creditor_identification']['registration_id'] ?? false;
    }

    public function isBicRequired(string $countryCode): bool
    {
        $spec = $this->getRequiredFields($countryCode);

        return $spec['creditor_agent']['bic'] ?? false;
    }

    public function isClearingSystemIdRequired(string $countryCode): bool
    {
        $spec = $this->getRequiredFields($countryCode);

        return $spec['creditor_agent']['clearing_system_id'] ?? false;
    }

    public function getIdentificationTypeForCountry(string $countryCode): string
    {
        // Based on section 5.2 Identity Type and corridor specs
        $typeMapping = [
            'CN' => 'REGISTRATION_ID',
            'US' => 'TAX_ID',
            'BR' => 'REGISTRATION_ID', // CPF/CNPJ
            'BE' => 'REGISTRATION_ID', // VAT, Company No.
            'IT' => 'REGISTRATION_ID', // VAT
            'MX' => 'REGISTRATION_ID',
            'AR' => 'REGISTRATION_ID',
        ];

        return $typeMapping[strtoupper($countryCode)] ?? 'REGISTRATION_ID';
    }

    public function isCountrySupported(string $countryCode): bool
    {
        $countryCode = strtoupper($countryCode);

        // Based on section 5.4 and node_api.txt corridor specifications
        $supportedCountries = [
            'US', 'IN', 'ID', 'VN', 'NG', 'NP', 'PK', 'CN', 'UK', 'GB', 'KR', 'BD', 'UG',
            'BR', 'MX', 'AR', 'BE', 'IT', 'UY', 'PE', 'MY', 'TR', 'ZA', 'FR', 'NL', 'PL',
            'CL', 'CO', 'EC', 'SA'
        ];

        return in_array($countryCode, $supportedCountries, true);
    }
}
