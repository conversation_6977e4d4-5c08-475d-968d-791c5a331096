<?xml version="1.0" encoding="UTF-8" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services
        http://symfony.com/schema/dic/services/services-1.0.xsd">

    <parameters>
        <parameter key="ipid_timeout">30</parameter>
        <parameter key="ipid_retry_attempts">3</parameter>
        <parameter key="ipid.enabled_countries" type="collection"></parameter>
        <parameter key="ipid.enabled_regions" type="collection">
            <parameter>EU</parameter>
        </parameter>
    </parameters>

    <services>
        <!-- Country Registry Service -->
        <service id="evp_ipid.country_registry" class="Evp\Bundle\IpidBundle\Service\IpidCountryRegistry">
            <argument>%ipid.enabled_countries%</argument>
            <argument>%ipid.enabled_regions%</argument>
        </service>

        <!-- Country Checker Service -->
        <service id="evp_ipid.country_checker" class="Evp\Bundle\IpidBundle\Service\IpidCountryChecker">
            <argument type="service" id="evp_ipid.country_registry" />
        </service>

        <service id="evp_ipid.transfer_helper" class="Evp\Bundle\IpidBundle\Service\IpidTransferHelper" />

        <service id="evp_ipid.encryption_service" class="Evp\Bundle\IpidBundle\Service\OpenPgpEncryptionService">
            <argument>%env(IPID_CLIENT_PRIVATE_KEY)%</argument>
        </service>

        <service id="evp_ipid.node_selector" class="Evp\Bundle\IpidBundle\Service\IpidNodeSelector" />

        <service id="evp_ipid.corridor_specification" class="Evp\Bundle\IpidBundle\Service\IpidCorridorSpecification" />

        <service id="evp_ipid.response_denormalizer" class="Evp\Bundle\IpidBundle\Normalizer\IpidResponseDenormalizer" />

        <service id="evp_ipid.api_client" class="Evp\Bundle\IpidBundle\Service\IpidApiClient">
            <argument type="service" id="http_client" />
            <argument type="service" id="evp_ipid.response_denormalizer" />
            <argument>%env(IPID_API_ENDPOINT)%</argument>
            <argument>%env(IPID_API_KEY)%</argument>
            <argument>%env(IPID_CUSTOMER_ID)%</argument>
            <argument type="service" id="logger" />
            <argument>%env(int:default:ipid_timeout:IPID_API_TIMEOUT)%</argument>
            <argument>%env(int:default:ipid_retry_attempts:IPID_API_RETRY_ATTEMPTS)%</argument>
        </service>

        <service id="evp_ipid.request_builder" class="Evp\Bundle\IpidBundle\Service\IpidRequestBuilder">
            <argument type="service" id="evp_ipid.transfer_helper" />
            <argument type="service" id="evp_ipid.encryption_service" />
            <argument type="service" id="evp_ipid.api_client" />
            <argument type="service" id="evp_ipid.node_selector" />
            <argument type="service" id="evp_ipid.corridor_specification" />
        </service>

        <!-- Repositories -->
        <service id="evp_ipid.api_response_repository" class="Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository">
            <factory service="doctrine.orm.entity_manager" method="getRepository" />
            <argument>Evp\Bundle\IpidBundle\Entity\IpidApiResponse</argument>
        </service>

        <service id="evp_ipid.consent_repository" class="Evp\Bundle\IpidBundle\Repository\TransferIpidConsentRepository">
            <factory service="doctrine.orm.entity_manager" method="getRepository" />
            <argument>Evp\Bundle\IpidBundle\Entity\TransferIpidConsent</argument>
        </service>

        <!-- OAuth Service -->
        <service id="evp_ipid.oauth_service" class="Evp\Bundle\IpidBundle\Service\IpidOAuthService">
            <argument type="service" id="evp_ipid.api_client" />
            <argument type="service" id="logger" />
            <argument>%env(IPID_CLIENT_ID)%</argument>
            <argument>%env(IPID_CLIENT_SECRET)%</argument>
        </service>

        <!-- Focused Services -->
        <service id="evp_ipid.transfer_validator" class="Evp\Bundle\IpidBundle\Service\Validation\IpidTransferValidator">
            <argument type="service" id="evp_ipid.country_checker" />
            <argument type="service" id="evp_ipid.consent_repository" />
            <argument type="service" id="evp_ipid.corridor_specification" />
        </service>

        <service id="evp_ipid.cache_manager" class="Evp\Bundle\IpidBundle\Service\Cache\IpidCacheManager">
            <argument type="service" id="evp_ipid.api_response_repository" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_ipid.response_processor" class="Evp\Bundle\IpidBundle\Service\Processing\IpidResponseProcessor">
            <argument type="service" id="evp_ipid.api_response_repository" />
            <argument type="service" id="evp_ipid.consent_repository" />
        </service>

        <service id="evp_ipid.error_handler" class="Evp\Bundle\IpidBundle\Service\ErrorHandling\IpidErrorHandler">
            <argument type="service" id="logger" />
        </service>

        <!-- Orchestrator Service -->
        <service id="evp_ipid.validation_orchestrator" class="Evp\Bundle\IpidBundle\Service\IpidValidationOrchestrator">
            <argument type="service" id="evp_ipid.transfer_validator" />
            <argument type="service" id="evp_ipid.request_builder" />
            <argument type="service" id="evp_ipid.cache_manager" />
            <argument type="service" id="evp_ipid.api_client" />
            <argument type="service" id="evp_ipid.response_processor" />
            <argument type="service" id="evp_ipid.error_handler" />
            <argument type="service" id="evp_ipid.transfer_helper" />
            <argument type="service" id="evp_ipid.country_checker" />
            <argument type="service" id="logger" />
        </service>

        <!-- Main Validation Service (Backward Compatibility) -->
        <service id="evp_ipid.validation_service" class="Evp\Bundle\IpidBundle\Service\IpidValidationService">
            <argument type="service" id="evp_ipid.validation_orchestrator" />
        </service>

        <service id="evp_ipid.validation_step" class="Evp\Bundle\IpidBundle\Step\IpidValidationStep">
            <argument type="service" id="evp_ipid.validation_service" />
            <argument type="service" id="logger" />
            <tag name="bank_transfer.step" />
        </service>

    </services>
</container>
