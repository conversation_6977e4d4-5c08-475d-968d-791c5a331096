<?xml version="1.0" encoding="utf-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping http://doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Evp\Bundle\IpidBundle\Entity\IpidApiResponse" table="ipid_api_response">
        <indexes>
            <index name="idx_request_hash_country" columns="request_hash,country"/>
            <index name="idx_created_at" columns="created_at"/>
            <index name="idx_response_code" columns="response_code"/>
        </indexes>
        <id name="id" type="integer" column="id">
            <generator strategy="IDENTITY"/>
        </id>
        <field name="requestHash" type="string" column="request_hash" length="64" nullable="false"/>
        <field name="country" type="string" column="country" length="2" nullable="false"/>
        <field name="matchScore" type="float" column="match_score" nullable="true"/>
        <field name="matchLevel" type="string" column="match_level" length="32" nullable="true"/>
        <field name="responseStatus" type="string" column="response_status" length="32" nullable="false"/>
        <field name="responseCode" type="string" column="response_code" length="4" nullable="false"/>
        <field name="responseMessage" type="string" column="response_message" length="255" nullable="false"/>
        <field name="requiresConsent" type="boolean" column="requires_consent" nullable="true"/>
        <field name="createdAt" type="datetime" column="created_at" nullable="false"/>
        <field name="vopIdMatch" type="string" column="vop_id_match" length="16" nullable="true"/>
        <field name="vopNameMatch" type="string" column="vop_name_match" length="16" nullable="true"/>
        <field name="copMatched" type="boolean" column="cop_matched" nullable="true"/>
        <field name="copReason" type="string" column="cop_reason" length="16" nullable="true"/>
        <field name="reasonCode" type="string" column="reason_code" length="16" nullable="true"/>
    </entity>
</doctrine-mapping>
