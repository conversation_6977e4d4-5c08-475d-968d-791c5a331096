<?xml version="1.0" encoding="utf-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping http://doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Evp\Bundle\IpidBundle\Entity\TransferIpidConsent" table="transfer_ipid_consent" repository-class="Evp\Bundle\IpidBundle\Repository\TransferIpidConsentRepository">
        <indexes>
            <index name="idx_transfer_id" columns="transfer_id"/>
            <index name="idx_consent_status" columns="consent_status"/>
            <index name="idx_created_at" columns="created_at"/>
        </indexes>
        <id name="id" type="integer" column="id">
            <generator strategy="IDENTITY"/>
        </id>
        <field name="transferId" type="integer" column="transfer_id" nullable="false"/>
        <field name="consentStatus" type="string" column="consent_status" length="32" nullable="false"/>
        <field name="matchLevel" type="string" column="match_level" length="32" nullable="false"/>
        <field name="requiresManualReview" type="boolean" column="requires_manual_review" nullable="false">
            <options>
                <option name="default">false</option>
            </options>
        </field>
        <field name="validationNotes" type="text" column="validation_notes" nullable="true"/>
        <field name="createdAt" type="datetime" column="created_at" nullable="false"/>
        <field name="updatedAt" type="datetime" column="updated_at" nullable="true"/>
        <many-to-one field="ipidApiResponse" target-entity="Evp\Bundle\IpidBundle\Entity\IpidApiResponse">
            <join-column name="ipid_api_response_id" referenced-column-name="id" nullable="false"/>
        </many-to-one>
    </entity>
</doctrine-mapping> 