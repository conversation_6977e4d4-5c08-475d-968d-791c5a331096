<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Entity;

use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use PHPUnit\Framework\TestCase;

final class IpidApiResponseTest extends TestCase
{
    private IpidApiResponse $apiResponse;

    protected function setUp(): void
    {
        $this->apiResponse = new IpidApiResponse();
    }

    public function testConstructorInitializesCreatedAt(): void
    {
        $response = new IpidApiResponse();
        
        self::assertInstanceOf(\DateTimeInterface::class, $response->getCreatedAt());
        self::assertEquals(date('Y-m-d'), $response->getCreatedAt()->format('Y-m-d'));
    }

    public function testSetAndGetRequestHash(): void
    {
        $requestHash = 'abc123def456';
        
        $result = $this->apiResponse->setRequestHash($requestHash);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($requestHash, $this->apiResponse->getRequestHash());
    }

    public function testSetAndGetCountry(): void
    {
        $country = 'GB';
        
        $result = $this->apiResponse->setCountry($country);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($country, $this->apiResponse->getCountry());
    }

    public function testSetAndGetMatchScore(): void
    {
        $matchScore = 0.85;
        
        $result = $this->apiResponse->setMatchScore($matchScore);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($matchScore, $this->apiResponse->getMatchScore());
    }

    public function testSetMatchScoreWithNull(): void
    {
        $this->apiResponse->setMatchScore(0.5);
        $result = $this->apiResponse->setMatchScore(null);
        
        self::assertSame($this->apiResponse, $result);
        self::assertNull($this->apiResponse->getMatchScore());
    }

    public function testSetAndGetMatchLevel(): void
    {
        $matchLevel = 'strong';
        
        $result = $this->apiResponse->setMatchLevel($matchLevel);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($matchLevel, $this->apiResponse->getMatchLevel());
    }

    public function testSetAndGetResponseStatus(): void
    {
        $responseStatus = 'success';
        
        $result = $this->apiResponse->setResponseStatus($responseStatus);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($responseStatus, $this->apiResponse->getResponseStatus());
    }

    public function testSetAndGetResponseCode(): void
    {
        $responseCode = '2000';
        
        $result = $this->apiResponse->setResponseCode($responseCode);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($responseCode, $this->apiResponse->getResponseCode());
    }

    public function testSetAndGetResponseMessage(): void
    {
        $responseMessage = 'ValidationSucceeded';
        
        $result = $this->apiResponse->setResponseMessage($responseMessage);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($responseMessage, $this->apiResponse->getResponseMessage());
    }

    public function testSetAndGetRequiresConsent(): void
    {
        $requiresConsent = true;
        
        $result = $this->apiResponse->setRequiresConsent($requiresConsent);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($requiresConsent, $this->apiResponse->getRequiresConsent());
    }

    public function testSetAndGetCreatedAt(): void
    {
        $createdAt = new \DateTime('2025-01-01 12:00:00');
        
        $result = $this->apiResponse->setCreatedAt($createdAt);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($createdAt, $this->apiResponse->getCreatedAt());
    }

    public function testSetAndGetVopIdMatch(): void
    {
        $vopIdMatch = 'MTCH';
        
        $result = $this->apiResponse->setVopIdMatch($vopIdMatch);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($vopIdMatch, $this->apiResponse->getVopIdMatch());
    }

    public function testSetAndGetVopNameMatch(): void
    {
        $vopNameMatch = 'CMTC';
        
        $result = $this->apiResponse->setVopNameMatch($vopNameMatch);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($vopNameMatch, $this->apiResponse->getVopNameMatch());
    }

    public function testSetAndGetCopMatched(): void
    {
        $copMatched = true;
        
        $result = $this->apiResponse->setCopMatched($copMatched);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($copMatched, $this->apiResponse->getCopMatched());
    }

    public function testSetAndGetCopReason(): void
    {
        $copReason = 'MATC';
        
        $result = $this->apiResponse->setCopReason($copReason);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($copReason, $this->apiResponse->getCopReason());
    }

    public function testSetAndGetReasonCode(): void
    {
        $reasonCode = 'ACCOUNT_INVALID';
        
        $result = $this->apiResponse->setReasonCode($reasonCode);
        
        self::assertSame($this->apiResponse, $result);
        self::assertEquals($reasonCode, $this->apiResponse->getReasonCode());
    }

    /**
     * @dataProvider isExpiredDataProvider
     */
    public function testIsExpired(
        \DateTimeInterface $createdAt,
        ?\DateTimeInterface $now,
        bool $expectedExpired
    ): void {
        $this->apiResponse->setCreatedAt($createdAt);
        
        $result = $this->apiResponse->isExpired($now);
        
        self::assertEquals($expectedExpired, $result);
    }

    public function isExpiredDataProvider(): array
    {
        $baseDate = new \DateTime('2025-01-01 12:00:00');
        
        return [
            'Not expired - created today' => [
                'createdAt' => $baseDate,
                'now' => $baseDate,
                'expectedExpired' => false,
            ],
            'Not expired - within cache duration' => [
                'createdAt' => $baseDate,
                'now' => (clone $baseDate)->modify('+29 days'),
                'expectedExpired' => false,
            ],
            'Expired - exactly at cache duration' => [
                'createdAt' => $baseDate,
                'now' => (clone $baseDate)->modify('+30 days'),
                'expectedExpired' => true,
            ],
            'Expired - beyond cache duration' => [
                'createdAt' => $baseDate,
                'now' => (clone $baseDate)->modify('+31 days'),
                'expectedExpired' => true,
            ],
            'Not expired - using current time by default' => [
                'createdAt' => new \DateTime(),
                'now' => null,
                'expectedExpired' => false,
            ],
        ];
    }

    /**
     * @dataProvider isSuccessfulDataProvider
     */
    public function testIsSuccessful(string $responseCode, bool $expectedSuccessful): void
    {
        $this->apiResponse->setResponseCode($responseCode);
        
        $result = $this->apiResponse->isSuccessful();
        
        self::assertEquals($expectedSuccessful, $result);
    }

    public function isSuccessfulDataProvider(): array
    {
        return [
            'Successful validation - 2000' => [
                'responseCode' => '2000',
                'expectedSuccessful' => true,
            ],
            'Successful format validation - 2001' => [
                'responseCode' => '2001',
                'expectedSuccessful' => true,
            ],
            'Successful account active - 2002' => [
                'responseCode' => '2002',
                'expectedSuccessful' => true,
            ],
            'Account not found - 2100' => [
                'responseCode' => '2100',
                'expectedSuccessful' => false,
            ],
            'Account flagged - 2101' => [
                'responseCode' => '2101',
                'expectedSuccessful' => false,
            ],
            'Invalid agent - 2102' => [
                'responseCode' => '2102',
                'expectedSuccessful' => false,
            ],
            'Could not validate - 2103' => [
                'responseCode' => '2103',
                'expectedSuccessful' => false,
            ],
            'Name match failed - 2104' => [
                'responseCode' => '2104',
                'expectedSuccessful' => false,
            ],
            'Format validation failed - 2105' => [
                'responseCode' => '2105',
                'expectedSuccessful' => false,
            ],
            'Invalid registration ID - 2106' => [
                'responseCode' => '2106',
                'expectedSuccessful' => false,
            ],
            'Client error - 4001' => [
                'responseCode' => '4001',
                'expectedSuccessful' => false,
            ],
            'Server error - 5000' => [
                'responseCode' => '5000',
                'expectedSuccessful' => false,
            ],
        ];
    }

    /**
     * @dataProvider hasHighMatchDataProvider
     */
    public function testHasHighMatch(?float $matchScore, bool $expectedHighMatch): void
    {
        $this->apiResponse->setMatchScore($matchScore);
        
        $result = $this->apiResponse->hasHighMatch();
        
        self::assertEquals($expectedHighMatch, $result);
    }

    public function hasHighMatchDataProvider(): array
    {
        return [
            'Null match score' => [
                'matchScore' => null,
                'expectedHighMatch' => false,
            ],
            'Zero match score' => [
                'matchScore' => 0.0,
                'expectedHighMatch' => false,
            ],
            'Low match score' => [
                'matchScore' => 0.5,
                'expectedHighMatch' => false,
            ],
            'Threshold match score' => [
                'matchScore' => 0.8,
                'expectedHighMatch' => true,
            ],
            'High match score' => [
                'matchScore' => 0.95,
                'expectedHighMatch' => true,
            ],
            'Perfect match score' => [
                'matchScore' => 1.0,
                'expectedHighMatch' => true,
            ],
        ];
    }

    public function testCacheDurationConstant(): void
    {
        self::assertEquals(30, IpidApiResponse::CACHE_DURATION_DAYS);
    }

    public function testFluentInterface(): void
    {
        $result = $this->apiResponse
            ->setRequestHash('hash123')
            ->setCountry('US')
            ->setMatchScore(0.9)
            ->setMatchLevel('strong')
            ->setResponseStatus('success')
            ->setResponseCode('2000')
            ->setResponseMessage('Success')
            ->setRequiresConsent(false)
            ->setVopIdMatch('MTCH')
            ->setVopNameMatch('CMTC')
            ->setCopMatched(true)
            ->setCopReason('MATC')
            ->setReasonCode('SUCCESS')
        ;
        
        self::assertSame($this->apiResponse, $result);
    }

    public function testDefaultValuesAfterConstruction(): void
    {
        $response = new IpidApiResponse();
        
        self::assertNull($response->getMatchScore());
        self::assertNull($response->getMatchLevel());
        self::assertNull($response->getRequiresConsent());
        self::assertNull($response->getVopIdMatch());
        self::assertNull($response->getVopNameMatch());
        self::assertNull($response->getCopMatched());
        self::assertNull($response->getCopReason());
        self::assertNull($response->getReasonCode());
    }

    public function testIsExpiredWithPrecisionCheck(): void
    {
        $createdAt = new \DateTime('2025-01-01 12:00:00');
        $exactExpiry = (clone $createdAt)->modify('+30 days');
        $justBeforeExpiry = (clone $exactExpiry)->modify('-1 second');
        $justAfterExpiry = (clone $exactExpiry)->modify('+1 second');
        
        $this->apiResponse->setCreatedAt($createdAt);
        
        self::assertFalse($this->apiResponse->isExpired($justBeforeExpiry));
        self::assertTrue($this->apiResponse->isExpired($exactExpiry));
        self::assertTrue($this->apiResponse->isExpired($justAfterExpiry));
    }

    public function testCompleteApiResponseScenario(): void
    {
        $createdAt = new \DateTime('2025-01-01 12:00:00');
        
        $this->apiResponse
            ->setRequestHash('abc123def456')
            ->setCountry('GB')
            ->setMatchScore(0.95)
            ->setMatchLevel('strong')
            ->setResponseStatus('success')
            ->setResponseCode('2000')
            ->setResponseMessage('ValidationSucceeded')
            ->setRequiresConsent(false)
            ->setCreatedAt($createdAt)
            ->setCopMatched(true)
            ->setCopReason('MATC')
        ;
        
        // Test all business logic methods
        self::assertTrue($this->apiResponse->isSuccessful());
        self::assertTrue($this->apiResponse->hasHighMatch());
        self::assertFalse($this->apiResponse->isExpired($createdAt));
        self::assertTrue($this->apiResponse->isExpired((clone $createdAt)->modify('+31 days')));
        
        // Test all property getters
        self::assertEquals('abc123def456', $this->apiResponse->getRequestHash());
        self::assertEquals('GB', $this->apiResponse->getCountry());
        self::assertEquals(0.95, $this->apiResponse->getMatchScore());
        self::assertEquals('strong', $this->apiResponse->getMatchLevel());
        self::assertEquals('success', $this->apiResponse->getResponseStatus());
        self::assertEquals('2000', $this->apiResponse->getResponseCode());
        self::assertEquals('ValidationSucceeded', $this->apiResponse->getResponseMessage());
        self::assertFalse($this->apiResponse->getRequiresConsent());
        self::assertEquals($createdAt, $this->apiResponse->getCreatedAt());
        self::assertTrue($this->apiResponse->getCopMatched());
        self::assertEquals('MATC', $this->apiResponse->getCopReason());
    }
} 