<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Step;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;
use Evp\Bundle\IpidBundle\Service\IpidValidationService;
use Evp\Bundle\IpidBundle\Step\IpidValidationStep;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class IpidValidationStepTest extends TestCase
{
    private IpidValidationStep $step;
    private IpidValidationService $validationService;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->validationService = self::createMock(IpidValidationService::class);
        $this->logger = self::createMock(LoggerInterface::class);

        $this->step = new IpidValidationStep(
            $this->validationService,
            $this->logger
        );
    }

    public function testApplyWithValidTransferAndSuccessfulValidation(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::atLeastOnce())
            ->method('getId')
            ->willReturn(123)
        ;

        $validationResult = self::createMock(IpidValidationResult::class);
        $validationResult
            ->expects(self::once())
            ->method('getStatus')
            ->willReturn('success')
        ;
        $validationResult
            ->expects(self::once())
            ->method('isSuccessful')
            ->willReturn(true)
        ;
        $validationResult
            ->expects(self::once())
            ->method('getConsent')
            ->willReturn(null)
        ;
        $validationResult
            ->expects(self::once())
            ->method('requiresManualReview')
            ->willReturn(false)
        ;

        $this->validationService
            ->expects(self::once())
            ->method('validateTransfer')
            ->with($transfer)
            ->willReturn($validationResult)
        ;

        $this->logger
            ->expects(self::once())
            ->method('info')
            ->with('iPiD validation step completed successfully', [
                'transfer_id' => 123,
                'validation_status' => 'success',
                'is_successful' => true,
                'has_consent' => false,
                'requires_manual_review' => false,
            ])
        ;

        $result = $this->step->apply($transfer);

        self::assertTrue($result);
    }

    public function testApplyWithValidTransferButFailedValidation(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::atLeastOnce())
            ->method('getId')
            ->willReturn(456)
        ;

        $validationResult = self::createMock(IpidValidationResult::class);
        $validationResult
            ->expects(self::once())
            ->method('getStatus')
            ->willReturn('error')
        ;
        $validationResult
            ->expects(self::once())
            ->method('isSuccessful')
            ->willReturn(false)
        ;
        $validationResult
            ->expects(self::once())
            ->method('getConsent')
            ->willReturn(null)
        ;
        $validationResult
            ->expects(self::once())
            ->method('requiresManualReview')
            ->willReturn(false)
        ;

        $this->validationService
            ->expects(self::once())
            ->method('validateTransfer')
            ->with($transfer)
            ->willReturn($validationResult)
        ;

        $this->logger
            ->expects(self::once())
            ->method('info')
            ->with('iPiD validation step completed successfully', [
                'transfer_id' => 456,
                'validation_status' => 'error',
                'is_successful' => false,
                'has_consent' => false,
                'requires_manual_review' => false,
            ])
        ;

        $result = $this->step->apply($transfer);

        self::assertTrue($result); // Step never fails transfer - always returns true
    }

    public function testApplyWithValidTransferRequiringManualReview(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::atLeastOnce())
            ->method('getId')
            ->willReturn(789)
        ;

        $validationResult = self::createMock(IpidValidationResult::class);
        $validationResult
            ->expects(self::once())
            ->method('getStatus')
            ->willReturn('success')
        ;
        $validationResult
            ->expects(self::once())
            ->method('isSuccessful')
            ->willReturn(true)
        ;
        $validationResult
            ->expects(self::once())
            ->method('getConsent')
            ->willReturn(null)
        ;
        $validationResult
            ->expects(self::once())
            ->method('requiresManualReview')
            ->willReturn(true)
        ;

        $this->validationService
            ->expects(self::once())
            ->method('validateTransfer')
            ->with($transfer)
            ->willReturn($validationResult)
        ;

        $this->logger
            ->expects(self::once())
            ->method('info')
            ->with('iPiD validation step completed successfully', [
                'transfer_id' => 789,
                'validation_status' => 'success',
                'is_successful' => true,
                'has_consent' => false,
                'requires_manual_review' => true,
            ])
        ;

        $result = $this->step->apply($transfer);

        self::assertTrue($result);
    }

    public function testApplyWithInvalidTransferType(): void
    {
        $invalidTransfer = new \stdClass();

        $this->logger
            ->expects(self::once())
            ->method('debug')
            ->with('iPiD validation step skipped - not a TransferOut entity', self::isType('array'))
        ;

        $this->validationService
            ->expects(self::never())
            ->method('validateTransfer')
        ;

        $result = $this->step->apply($invalidTransfer);

        self::assertTrue($result); // Step never fails transfer - always returns true
    }

    public function testApplyWithNullTransfer(): void
    {
        $this->validationService
            ->expects(self::never())
            ->method('validateTransfer')
        ;

        $this->expectException(\TypeError::class);
        $this->expectExceptionMessage('get_class() expects parameter 1 to be object, null given');

        $this->step->apply(null);
    }

    /**
     * @dataProvider validationResultDataProvider
     */
    public function testApplyWithDifferentValidationResults(
        string $status,
        bool $canProceed,
        bool $requiresReview,
        bool $expectedStepResult
    ): void {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::atLeastOnce())
            ->method('getId')
            ->willReturn(999)
        ;

        $validationResult = self::createMock(IpidValidationResult::class);
        $validationResult
            ->expects(self::once())
            ->method('getStatus')
            ->willReturn($status)
        ;
        $validationResult
            ->expects(self::once())
            ->method('isSuccessful')
            ->willReturn($status === 'success')
        ;
        $validationResult
            ->expects(self::once())
            ->method('getConsent')
            ->willReturn(null)
        ;
        $validationResult
            ->expects(self::once())
            ->method('requiresManualReview')
            ->willReturn($requiresReview)
        ;

        $this->validationService
            ->expects(self::once())
            ->method('validateTransfer')
            ->with($transfer)
            ->willReturn($validationResult)
        ;

        $this->logger
            ->expects(self::once())
            ->method('info')
            ->with('iPiD validation step completed successfully', [
                'transfer_id' => 999,
                'validation_status' => $status,
                'is_successful' => $status === 'success',
                'has_consent' => false,
                'requires_manual_review' => $requiresReview,
            ])
        ;

        $result = $this->step->apply($transfer);

        // Step always returns true - never fails transfer
        self::assertTrue($result);
    }

    public function validationResultDataProvider(): array
    {
        return [
            'Success can proceed no review' => [
                'status' => 'success',
                'canProceed' => true,
                'requiresReview' => false,
                'expectedStepResult' => true,
            ],
            'Success can proceed with review' => [
                'status' => 'success',
                'canProceed' => true,
                'requiresReview' => true,
                'expectedStepResult' => true,
            ],
            'Success cannot proceed no review' => [
                'status' => 'success',
                'canProceed' => false,
                'requiresReview' => false,
                'expectedStepResult' => false,
            ],
            'Success cannot proceed with review' => [
                'status' => 'success',
                'canProceed' => false,
                'requiresReview' => true,
                'expectedStepResult' => false,
            ],
            'Error cannot proceed no review' => [
                'status' => 'error',
                'canProceed' => false,
                'requiresReview' => false,
                'expectedStepResult' => false,
            ],
            'Unsupported country cannot proceed' => [
                'status' => 'unsupported_country',
                'canProceed' => false,
                'requiresReview' => false,
                'expectedStepResult' => false,
            ],
            'Already validated can proceed' => [
                'status' => 'already_validated',
                'canProceed' => true,
                'requiresReview' => false,
                'expectedStepResult' => true,
            ],
        ];
    }

    public function testApplyLogsCorrectTransferType(): void
    {
        $nonTransferObject = new class {
            public function getId(): int
            {
                return 123;
            }
        };

        $this->logger
            ->expects(self::once())
            ->method('debug')
            ->with('iPiD validation step skipped - not a TransferOut entity', self::isType('array'))
        ;

        $result = $this->step->apply($nonTransferObject);

        self::assertTrue($result); // Step never fails transfer - always returns true
    }

    public function testApplyWithValidationServiceException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->expects(self::atLeastOnce())->method('getId')->willReturn(123);

        $this->validationService
            ->expects(self::once())
            ->method('validateTransfer')
            ->with($transfer)
            ->willThrowException(new \RuntimeException('Validation service error'))
        ;

        $this->logger
            ->expects(self::once())
            ->method('error')
            ->with('iPiD validation step encountered an error', self::isType('array'))
        ;

        // Step should not throw exception - it should handle errors gracefully
        $result = $this->step->apply($transfer);
        self::assertTrue($result); // Always returns true
    }

    public function testStepCanBeUsedMultipleTimes(): void
    {
        $transfer1 = self::createMock(TransferOut::class);
        $transfer1
            ->expects(self::atLeastOnce())
            ->method('getId')
            ->willReturn(111)
        ;

        $transfer2 = self::createMock(TransferOut::class);
        $transfer2
            ->expects(self::atLeastOnce())
            ->method('getId')
            ->willReturn(222)
        ;

        $validationResult1 = self::createMock(IpidValidationResult::class);
        $validationResult1
            ->expects(self::once())
            ->method('getStatus')
            ->willReturn('success')
        ;
        $validationResult1
            ->expects(self::once())
            ->method('isSuccessful')
            ->willReturn(true)
        ;
        $validationResult1
            ->expects(self::once())
            ->method('getConsent')
            ->willReturn(null)
        ;
        $validationResult1
            ->expects(self::once())
            ->method('requiresManualReview')
            ->willReturn(false)
        ;

        $validationResult2 = self::createMock(IpidValidationResult::class);
        $validationResult2
            ->expects(self::once())
            ->method('getStatus')
            ->willReturn('error')
        ;
        $validationResult2
            ->expects(self::once())
            ->method('isSuccessful')
            ->willReturn(false)
        ;
        $validationResult2
            ->expects(self::once())
            ->method('getConsent')
            ->willReturn(null)
        ;
        $validationResult2
            ->expects(self::once())
            ->method('requiresManualReview')
            ->willReturn(false)
        ;

        $this->validationService
            ->expects(self::exactly(2))
            ->method('validateTransfer')
            ->willReturnOnConsecutiveCalls($validationResult1, $validationResult2)
        ;

        $result1 = $this->step->apply($transfer1);
        $result2 = $this->step->apply($transfer2);

        // Both should return true - step never fails transfer
        self::assertTrue($result1);
        self::assertTrue($result2);
    }
} 