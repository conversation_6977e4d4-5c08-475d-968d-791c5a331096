<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\DTO\IpidValidationResponse;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;
use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository;
use Evp\Bundle\IpidBundle\Repository\TransferIpidConsentRepository;
use Evp\Bundle\IpidBundle\Service\IpidApiClient;
use Evp\Bundle\IpidBundle\Service\IpidCorridorSpecification;
use Evp\Bundle\IpidBundle\Service\IpidCountryChecker;
use Evp\Bundle\IpidBundle\Service\IpidRequestBuilder;
use Evp\Bundle\IpidBundle\Service\IpidTransferHelper;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;
use Evp\Bundle\IpidBundle\Service\IpidValidationService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class IpidValidationServiceTest extends TestCase
{
    private IpidValidationService $service;
    private IpidApiClient $apiClient;
    private IpidRequestBuilder $requestBuilder;
    private IpidApiResponseRepository $ipidApiResponseRepository;
    private TransferIpidConsentRepository $transferIpidConsentRepository;
    private IpidTransferHelper $transferHelper;
    private IpidCorridorSpecification $corridorSpecification;
    private IpidCountryChecker $countryChecker;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->apiClient = self::createMock(IpidApiClient::class);
        $this->requestBuilder = self::createMock(IpidRequestBuilder::class);
        $this->ipidApiResponseRepository = self::createMock(IpidApiResponseRepository::class);
        $this->transferIpidConsentRepository = self::createMock(TransferIpidConsentRepository::class);
        $this->transferHelper = self::createMock(IpidTransferHelper::class);
        $this->corridorSpecification = self::createMock(IpidCorridorSpecification::class);
        $this->countryChecker = self::createMock(IpidCountryChecker::class);
        $this->logger = self::createMock(LoggerInterface::class);

        $this->service = new IpidValidationService(
            $this->apiClient,
            $this->requestBuilder,
            $this->ipidApiResponseRepository,
            $this->transferIpidConsentRepository,
            $this->transferHelper,
            $this->corridorSpecification,
            $this->countryChecker,
            $this->logger
        );
    }

    /**
     * @dataProvider validateTransferSuccessDataProvider
     */
    public function testValidateTransferSuccess(
        string $country,
        float $matchScore,
        string $matchLevel,
        string $expectedConsentStatus
    ): void {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::atLeastOnce())
            ->method('getId')
            ->willReturn(123)
        ;

        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('extractCountryFromTransfer')
            ->with($transfer)
            ->willReturn($country)
        ;

        // Mock the country checker
        $this->countryChecker
            ->expects(self::once())
            ->method('supportsValidation')
            ->with($country)
            ->willReturn(true)
        ;

        $this->corridorSpecification
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with($country)
            ->willReturn(true)
        ;

        // Mock additional country checker methods for logging
        $this->countryChecker
            ->expects(self::once())
            ->method('getValidationSchemeForCountry')
            ->with($country)
            ->willReturn('VOP')
        ;

        $this->countryChecker
            ->expects(self::once())
            ->method('getRegionForCountry')
            ->with($country)
            ->willReturn('EU')
        ;

        $this->transferIpidConsentRepository
            ->expects(self::once())
            ->method('findByTransferId')
            ->with(123)
            ->willReturn(null)
        ;

        $request = self::createMock(IpidValidationRequest::class);
        $request
            ->expects(self::atLeastOnce())
            ->method('getNodeId')
            ->willReturn('test-node-01')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getClearingSystemId')
            ->willReturn('123456')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getBic')
            ->willReturn('TESTGB2LXXX')
        ;

        $this->requestBuilder
            ->expects(self::once())
            ->method('buildRequest')
            ->with($transfer)
            ->willReturn($request)
        ;

        // Mock helper methods for hash calculation
        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('getBeneficiaryAccount')
            ->with($transfer)
            ->willReturn('********')
        ;
        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('getBeneficiaryName')
            ->with($transfer)
            ->willReturn('John Smith')
        ;
        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('extractPersonCodeFromTransfer')
            ->with($transfer)
            ->willReturn(null)
        ;

        // Mock the cached response lookup - return null to trigger API call
        $this->ipidApiResponseRepository
            ->expects(self::once())
            ->method('findValidByRequestHashAndCountry')
            ->with(self::isType('string'), $country)
            ->willReturn(null)
        ;

        $response = self::createMock(IpidValidationResponse::class);
        $response
            ->expects(self::once())
            ->method('getMatchScore')
            ->willReturn($matchScore)
        ;
        $response
            ->expects(self::once())
            ->method('getMatchLevel')
            ->willReturn($matchLevel)
        ;
        $response
            ->expects(self::once())
            ->method('getStatus')
            ->willReturn('success')
        ;
        $response
            ->expects(self::once())
            ->method('getResponseCode')
            ->willReturn('2000')
        ;
        $response
            ->expects(self::once())
            ->method('getResponseMessage')
            ->willReturn('Success')
        ;
        $response
            ->expects(self::once())
            ->method('getRequiresConsent')
            ->willReturn(false)
        ;
        $response
            ->expects(self::once())
            ->method('getVopIdMatch')
            ->willReturn(null)
        ;
        $response
            ->expects(self::once())
            ->method('getVopNameMatch')
            ->willReturn(null)
        ;
        $response
            ->expects(self::once())
            ->method('getCopMatched')
            ->willReturn(null)
        ;
        $response
            ->expects(self::once())
            ->method('getCopReason')
            ->willReturn(null)
        ;
        $response
            ->expects(self::once())
            ->method('getReasonCode')
            ->willReturn(null)
        ;

        $this->apiClient
            ->expects(self::once())
            ->method('sendValidationRequest')
            ->with($request)
            ->willReturn($response)
        ;

        $apiResponse = self::createMock(IpidApiResponse::class);
        $apiResponse
            ->expects(self::atLeastOnce())
            ->method('getMatchLevel')
            ->willReturn($matchLevel)
        ;
        $apiResponse
            ->expects(self::atLeastOnce())
            ->method('isSuccessful')
            ->willReturn(true)
        ;
        $apiResponse
            ->expects(self::atLeastOnce())
            ->method('getRequiresConsent')
            ->willReturn(false)
        ;

        // Simplify mock expectations - just check that save is called
        $this->ipidApiResponseRepository
            ->expects(self::once())
            ->method('save')
            ->willReturn($apiResponse)
        ;

        $consent = self::createMock(TransferIpidConsent::class);
        $this->transferIpidConsentRepository
            ->expects(self::once())
            ->method('save')
            ->willReturn($consent)
        ;

        $result = $this->service->validateTransfer($transfer);

        self::assertInstanceOf(IpidValidationResult::class, $result);
    }

    public function validateTransferSuccessDataProvider(): array
    {
        return [
            'Strong match for UK transfer' => [
                'country' => 'GB',
                'matchScore' => 1.0,
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_STRONG,
                'expectedConsentStatus' => TransferIpidConsent::CONSENT_STATUS_GRANTED,
            ],
            'Partial match for German transfer' => [
                'country' => 'DE',
                'matchScore' => 0.7,
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_PARTIAL,
                'expectedConsentStatus' => TransferIpidConsent::CONSENT_STATUS_PENDING,
            ],
            'Weak match for US transfer' => [
                'country' => 'US',
                'matchScore' => 0.3,
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_WEAK,
                'expectedConsentStatus' => TransferIpidConsent::CONSENT_STATUS_PENDING,
            ],
        ];
    }

    public function testValidateTransferUnsupportedCountry(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::once())
            ->method('getId')
            ->willReturn(123)
        ;

        $this->transferHelper
            ->expects(self::once())
            ->method('extractCountryFromTransfer')
            ->with($transfer)
            ->willReturn('XX')
        ;

        // Mock country checker to return false for unsupported country
        $this->countryChecker
            ->expects(self::once())
            ->method('supportsValidation')
            ->with('XX')
            ->willReturn(false)
        ;

        // Mock the corridor specification to also return false  
        $this->corridorSpecification
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with('XX')
            ->willReturn(false)
        ;

        // Mock additional methods for logging
        $this->countryChecker
            ->expects(self::once())
            ->method('getEnabledCountries')
            ->willReturn(['DE', 'FR', 'NL'])
        ;

        $this->countryChecker
            ->expects(self::once())
            ->method('getEnabledRegions')
            ->willReturn(['EU'])
        ;

        $this->countryChecker
            ->expects(self::once())
            ->method('getValidationSchemeForCountry')
            ->with('XX')
            ->willReturn('GLOBAL')
        ;

        $this->countryChecker
            ->expects(self::once())
            ->method('getRegionForCountry')
            ->with('XX')
            ->willReturn(null)
        ;

        $this->logger
            ->expects(self::once())
            ->method('warning')
            ->with('iPiD validation attempted for unsupported country', [
                'transfer_id' => 123,
                'country' => 'XX',
                'enabled_countries' => ['DE', 'FR', 'NL'],
                'enabled_regions' => ['EU'],
                'validation_scheme' => 'GLOBAL',
                'country_region' => null
            ])
        ;

        $result = $this->service->validateTransfer($transfer);

        self::assertEquals('unsupported_country', $result->getStatus());
    }

    public function testValidateTransferAlreadyValidated(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::atLeastOnce())
            ->method('getId')
            ->willReturn(123)
        ;

        $this->transferHelper
            ->expects(self::once())
            ->method('extractCountryFromTransfer')
            ->with($transfer)
            ->willReturn('GB')
        ;

        // Mock country checker to return true for supported country
        $this->countryChecker
            ->expects(self::once())
            ->method('supportsValidation')
            ->with('GB')
            ->willReturn(true)
        ;

        $this->corridorSpecification
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with('GB')
            ->willReturn(true)
        ;

        // Mock additional country checker methods for successful validation logging
        $this->countryChecker
            ->expects(self::once())
            ->method('getValidationSchemeForCountry')
            ->with('GB')
            ->willReturn('COP')
        ;

        $this->countryChecker
            ->expects(self::once())
            ->method('getRegionForCountry')
            ->with('GB')
            ->willReturn('UK')
        ;

        $existingConsent = self::createMock(TransferIpidConsent::class);
        $this->transferIpidConsentRepository
            ->expects(self::once())
            ->method('findByTransferId')
            ->with(123)
            ->willReturn($existingConsent)
        ;

        $result = $this->service->validateTransfer($transfer);

        self::assertEquals('already_validated', $result->getStatus());
    }

    public function testValidateTransferUseCachedResponse(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::atLeastOnce())
            ->method('getId')
            ->willReturn(123)
        ;

        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('extractCountryFromTransfer')
            ->with($transfer)
            ->willReturn('GB')
        ;

        // Mock country checker to return true for supported country
        $this->countryChecker
            ->expects(self::once())
            ->method('supportsValidation')
            ->with('GB')
            ->willReturn(true)
        ;

        $this->corridorSpecification
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with('GB')
            ->willReturn(true)
        ;

        // Mock additional country checker methods for successful validation logging
        $this->countryChecker
            ->expects(self::once())
            ->method('getValidationSchemeForCountry')
            ->with('GB')
            ->willReturn('COP')
        ;

        $this->countryChecker
            ->expects(self::once())
            ->method('getRegionForCountry')
            ->with('GB')
            ->willReturn('UK')
        ;

        $this->transferIpidConsentRepository
            ->expects(self::once())
            ->method('findByTransferId')
            ->with(123)
            ->willReturn(null)
        ;

        $request = self::createMock(IpidValidationRequest::class);
        $request
            ->expects(self::atLeastOnce())
            ->method('getNodeId')
            ->willReturn('gb-node-01')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getClearingSystemId')
            ->willReturn('112233')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getBic')
            ->willReturn('ABCDGB2LXXX')
        ;

        $this->requestBuilder
            ->expects(self::once())
            ->method('buildRequest')
            ->with($transfer)
            ->willReturn($request)
        ;

        // Mock helper methods for hash calculation (called during calculateRequestHash)
        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('getBeneficiaryAccount')
            ->with($transfer)
            ->willReturn('********')
        ;
        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('getBeneficiaryName')
            ->with($transfer)
            ->willReturn('Sherlock Holmes')
        ;
        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('extractPersonCodeFromTransfer')
            ->with($transfer)
            ->willReturn(null)
        ;

        $cachedResponse = self::createMock(IpidApiResponse::class);
        $cachedResponse
            ->expects(self::once())
            ->method('isExpired')
            ->willReturn(false)
        ;
        $cachedResponse
            ->expects(self::atLeastOnce())
            ->method('getMatchLevel')
            ->willReturn(TransferIpidConsent::MATCH_LEVEL_STRONG)
        ;
        $cachedResponse
            ->expects(self::atLeastOnce())
            ->method('isSuccessful')
            ->willReturn(true)
        ;
        $cachedResponse
            ->expects(self::atLeastOnce())
            ->method('getRequiresConsent')
            ->willReturn(false)
        ;

        $this->ipidApiResponseRepository
            ->expects(self::once())
            ->method('findValidByRequestHashAndCountry')
            ->with(self::isType('string'), 'GB')
            ->willReturn($cachedResponse)
        ;

        // API client should NOT be called when using cached response
        $this->apiClient
            ->expects(self::never())
            ->method('sendValidationRequest')
        ;

        // API response repository save should NOT be called for cached responses
        $this->ipidApiResponseRepository
            ->expects(self::never())
            ->method('save')
        ;

        // Expect info log for successful validation
        $this->logger
            ->expects(self::exactly(2))
            ->method('info')
            ->withConsecutive(
                ['iPiD validation initiated for supported country', self::isType('array')],
                ['Using cached iPiD response', self::isType('array')]
            )
        ;

        $consent = self::createMock(TransferIpidConsent::class);
        $this->transferIpidConsentRepository
            ->expects(self::once())
            ->method('save')
            ->willReturn($consent)
        ;

        $result = $this->service->validateTransfer($transfer);

        self::assertInstanceOf(IpidValidationResult::class, $result);
    }

    public function testValidateTransferApiException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::atLeastOnce())
            ->method('getId')
            ->willReturn(123)
        ;

        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('extractCountryFromTransfer')
            ->with($transfer)
            ->willReturn('GB')
        ;

        // Mock country checker to return true for supported country
        $this->countryChecker
            ->expects(self::once())
            ->method('supportsValidation')
            ->with('GB')
            ->willReturn(true)
        ;

        $this->corridorSpecification
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with('GB')
            ->willReturn(true)
        ;

        // Mock additional country checker methods for successful validation logging
        $this->countryChecker
            ->expects(self::once())
            ->method('getValidationSchemeForCountry')
            ->with('GB')
            ->willReturn('COP')
        ;

        $this->countryChecker
            ->expects(self::once())
            ->method('getRegionForCountry')
            ->with('GB')
            ->willReturn('UK')
        ;

        $this->transferIpidConsentRepository
            ->expects(self::once())
            ->method('findByTransferId')
            ->with(123)
            ->willReturn(null)
        ;

        $request = self::createMock(IpidValidationRequest::class);
        $request
            ->expects(self::atLeastOnce())
            ->method('getNodeId')
            ->willReturn('gb-node-01')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getClearingSystemId')
            ->willReturn('112233')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getBic')
            ->willReturn('ABCDGB2LXXX')
        ;

        $this->requestBuilder
            ->expects(self::once())
            ->method('buildRequest')
            ->with($transfer)
            ->willReturn($request)
        ;

        // Mock helper methods for hash calculation
        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('getBeneficiaryAccount')
            ->with($transfer)
            ->willReturn('********')
        ;
        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('getBeneficiaryName')
            ->with($transfer)
            ->willReturn('Sherlock Holmes')
        ;
        $this->transferHelper
            ->expects(self::atLeastOnce())
            ->method('extractPersonCodeFromTransfer')
            ->with($transfer)
            ->willReturn(null)
        ;

        $this->ipidApiResponseRepository
            ->expects(self::once())
            ->method('findValidByRequestHashAndCountry')
            ->with(self::isType('string'), 'GB')
            ->willReturn(null)
        ;

        $apiException = new IpidApiException('API error', 'IPID_001');
        $this->apiClient
            ->expects(self::once())
            ->method('sendValidationRequest')
            ->with($request)
            ->willThrowException($apiException)
        ;

        $this->logger
            ->expects(self::once())
            ->method('error')
            ->with('iPiD API error during validation', [
                'transfer_id' => 123,
                'error' => 'API error',
                'error_code' => 'IPID_001'
            ])
        ;

        $result = $this->service->validateTransfer($transfer);

        self::assertEquals('error', $result->getStatus());
        self::assertEquals('API error', $result->getErrorMessage());
    }

    public function testValidateTransferUnexpectedException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::once())
            ->method('getId')
            ->willReturn(123)
        ;

        $this->transferHelper
            ->expects(self::once())
            ->method('extractCountryFromTransfer')
            ->with($transfer)
            ->willThrowException(new \Exception('Unexpected error'))
        ;

        $this->logger
            ->expects(self::once())
            ->method('error')
            ->with('Unexpected error during iPiD validation', [
                'transfer_id' => 123,
                'error' => 'Unexpected error'
            ])
        ;

        $result = $this->service->validateTransfer($transfer);

        self::assertEquals('error', $result->getStatus());
        self::assertEquals('Internal validation error', $result->getErrorMessage());
    }

    /**
     * @dataProvider consentStatusDataProvider
     */
    public function testDetermineConsentStatus(
        bool $isSuccessful,
        bool $requiresConsent,
        string $matchLevel,
        string $expectedStatus
    ): void {
        $apiResponse = self::createMock(IpidApiResponse::class);
        $apiResponse
            ->expects(self::once())
            ->method('isSuccessful')
            ->willReturn($isSuccessful)
        ;

        if ($isSuccessful) {
            $apiResponse
                ->expects(self::once())
                ->method('getRequiresConsent')
                ->willReturn($requiresConsent)
            ;

            if (!$requiresConsent) {
                $apiResponse
                    ->expects(self::once())
                    ->method('getMatchLevel')
                    ->willReturn($matchLevel)
                ;
            }
        }

        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('determineConsentStatus');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, $apiResponse);

        self::assertEquals($expectedStatus, $result);
    }

    public function consentStatusDataProvider(): array
    {
        return [
            'Failed validation results in denied consent' => [
                'isSuccessful' => false,
                'requiresConsent' => false,
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_NO_MATCH,
                'expectedStatus' => TransferIpidConsent::CONSENT_STATUS_DENIED,
            ],
            'Requires explicit consent' => [
                'isSuccessful' => true,
                'requiresConsent' => true,
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_STRONG,
                'expectedStatus' => TransferIpidConsent::CONSENT_STATUS_REQUIRED,
            ],
            'Strong match grants consent' => [
                'isSuccessful' => true,
                'requiresConsent' => false,
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_STRONG,
                'expectedStatus' => TransferIpidConsent::CONSENT_STATUS_GRANTED,
            ],
            'Partial match requires pending review' => [
                'isSuccessful' => true,
                'requiresConsent' => false,
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_PARTIAL,
                'expectedStatus' => TransferIpidConsent::CONSENT_STATUS_PENDING,
            ],
            'Weak match requires pending review' => [
                'isSuccessful' => true,
                'requiresConsent' => false,
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_WEAK,
                'expectedStatus' => TransferIpidConsent::CONSENT_STATUS_PENDING,
            ],
            'No match denies consent' => [
                'isSuccessful' => true,
                'requiresConsent' => false,
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_NO_MATCH,
                'expectedStatus' => TransferIpidConsent::CONSENT_STATUS_DENIED,
            ],
        ];
    }

    /**
     * @dataProvider manualReviewDataProvider
     */
    public function testShouldRequireManualReview(
        string $matchLevel,
        bool $isSuccessful,
        bool $expectedRequiresReview
    ): void {
        $apiResponse = self::createMock(IpidApiResponse::class);
        
        // Only expect isSuccessful call for strong match with unsuccessful response
        if ($matchLevel === TransferIpidConsent::MATCH_LEVEL_STRONG) {
            $apiResponse
                ->expects(self::atLeastOnce())
                ->method('isSuccessful')
                ->willReturn($isSuccessful)
            ;
        }

        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('shouldRequireManualReview');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, $matchLevel, $apiResponse);

        self::assertEquals($expectedRequiresReview, $result);
    }

    public function manualReviewDataProvider(): array
    {
        return [
            'Partial match requires review' => [
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_PARTIAL,
                'isSuccessful' => true,
                'expectedRequiresReview' => true,
            ],
            'Weak match requires review' => [
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_WEAK,
                'isSuccessful' => true,
                'expectedRequiresReview' => true,
            ],
            'Strong match with successful response does not require review' => [
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_STRONG,
                'isSuccessful' => true,
                'expectedRequiresReview' => false,
            ],
            'Strong match with unsuccessful response requires review' => [
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_STRONG,
                'isSuccessful' => false,
                'expectedRequiresReview' => true,
            ],
            'No match does not require review' => [
                'matchLevel' => TransferIpidConsent::MATCH_LEVEL_NO_MATCH,
                'isSuccessful' => false,
                'expectedRequiresReview' => false,
            ],
        ];
    }
} 