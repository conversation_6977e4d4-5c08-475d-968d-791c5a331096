<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\Service\IpidValidationOrchestrator;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;
use Evp\Bundle\IpidBundle\Service\IpidValidationService;
use PHPUnit\Framework\TestCase;

final class IpidValidationServiceTest extends TestCase
{
    private IpidValidationService $service;
    private IpidValidationOrchestrator $orchestrator;

    protected function setUp(): void
    {
        $this->orchestrator = self::createMock(IpidValidationOrchestrator::class);
        $this->service = new IpidValidationService($this->orchestrator);
    }

    public function testValidateTransferDelegatesToOrchestrator(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $expectedResult = self::createMock(IpidValidationResult::class);

        $this->orchestrator
            ->expects(self::once())
            ->method('validateTransfer')
            ->with($transfer)
            ->willReturn($expectedResult)
        ;

        $result = $this->service->validateTransfer($transfer);

        self::assertSame($expectedResult, $result);
    }
}