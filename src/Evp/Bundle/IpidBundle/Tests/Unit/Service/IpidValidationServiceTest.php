<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;
use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Service\Cache\IpidCacheManager;
use Evp\Bundle\IpidBundle\Service\ErrorHandling\IpidErrorHandler;
use Evp\Bundle\IpidBundle\Service\IpidApiClient;
use Evp\Bundle\IpidBundle\Service\IpidCountryChecker;
use Evp\Bundle\IpidBundle\Service\IpidRequestBuilder;
use Evp\Bundle\IpidBundle\Service\IpidTransferHelper;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;
use Evp\Bundle\IpidBundle\Service\IpidValidationService;
use Evp\Bundle\IpidBundle\Service\Processing\IpidResponseProcessor;
use Evp\Bundle\IpidBundle\Service\Validation\IpidTransferValidator;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

/**
 * Unit tests for the refactored IpidValidationService.
 *
 * Tests the main validation flow scenarios:
 * - Unsupported country handling
 * - Already validated transfer detection
 * - API exception handling
 *
 * The service now directly coordinates all focused services instead of using an orchestrator.
 */
final class IpidValidationServiceTest extends TestCase
{
    private IpidValidationService $service;
    private MockObject $transferValidator;
    private MockObject $requestBuilder;
    private MockObject $cacheManager;
    private MockObject $apiClient;
    private MockObject $responseProcessor;
    private MockObject $errorHandler;
    private MockObject $transferHelper;
    private MockObject $countryChecker;
    private MockObject $logger;

    protected function setUp(): void
    {
        $this->transferValidator = self::createMock(IpidTransferValidator::class);
        $this->requestBuilder = self::createMock(IpidRequestBuilder::class);
        $this->cacheManager = self::createMock(IpidCacheManager::class);
        $this->apiClient = self::createMock(IpidApiClient::class);
        $this->responseProcessor = self::createMock(IpidResponseProcessor::class);
        $this->errorHandler = self::createMock(IpidErrorHandler::class);
        $this->transferHelper = self::createMock(IpidTransferHelper::class);
        $this->countryChecker = self::createMock(IpidCountryChecker::class);
        $this->logger = self::createMock(LoggerInterface::class);

        $this->service = new IpidValidationService(
            $this->transferValidator,
            $this->requestBuilder,
            $this->cacheManager,
            $this->apiClient,
            $this->responseProcessor,
            $this->errorHandler,
            $this->transferHelper,
            $this->countryChecker,
            $this->logger
        );
    }

    public function testValidateTransferUnsupportedCountry(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->expects(self::once())->method('getId')->willReturn(123);

        $this->transferHelper
            ->expects(self::once())
            ->method('extractCountryFromTransfer')
            ->with($transfer)
            ->willReturn('XX');

        $this->transferValidator
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with('XX')
            ->willReturn(false);

        $this->countryChecker
            ->expects(self::once())
            ->method('getEnabledCountries')
            ->willReturn(['GB', 'DE', 'FR']);

        $this->countryChecker
            ->expects(self::once())
            ->method('getEnabledRegions')
            ->willReturn(['EU']);

        $this->countryChecker
            ->expects(self::once())
            ->method('getValidationSchemeForCountry')
            ->with('XX')
            ->willReturn('GLOBAL');

        $this->countryChecker
            ->expects(self::once())
            ->method('getRegionForCountry')
            ->with('XX')
            ->willReturn(null);

        $this->logger
            ->expects(self::once())
            ->method('warning')
            ->with('iPiD validation attempted for unsupported country', self::isType('array'));

        $result = $this->service->validateTransfer($transfer);

        self::assertEquals('unsupported_country', $result->getStatus());
    }

    public function testValidateTransferAlreadyValidated(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->expects(self::atLeastOnce())->method('getId')->willReturn(123);

        $this->transferHelper
            ->expects(self::once())
            ->method('extractCountryFromTransfer')
            ->with($transfer)
            ->willReturn('GB');

        $this->transferValidator
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with('GB')
            ->willReturn(true);

        $this->countryChecker
            ->expects(self::once())
            ->method('getValidationSchemeForCountry')
            ->with('GB')
            ->willReturn('COP');

        $this->countryChecker
            ->expects(self::once())
            ->method('getRegionForCountry')
            ->with('GB')
            ->willReturn('UK');

        $this->logger
            ->expects(self::once())
            ->method('info')
            ->with('iPiD validation initiated for supported country', self::isType('array'));

        $existingConsent = self::createMock(TransferIpidConsent::class);
        $this->transferValidator
            ->expects(self::once())
            ->method('findExistingConsent')
            ->with($transfer)
            ->willReturn($existingConsent);

        $result = $this->service->validateTransfer($transfer);

        self::assertEquals('already_validated', $result->getStatus());
    }

    public function testValidateTransferApiException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->expects(self::atLeastOnce())->method('getId')->willReturn(123);

        $this->transferHelper
            ->expects(self::once())
            ->method('extractCountryFromTransfer')
            ->with($transfer)
            ->willReturn('GB');

        $this->transferValidator
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with('GB')
            ->willReturn(true);

        $this->countryChecker
            ->expects(self::once())
            ->method('getValidationSchemeForCountry')
            ->with('GB')
            ->willReturn('COP');

        $this->countryChecker
            ->expects(self::once())
            ->method('getRegionForCountry')
            ->with('GB')
            ->willReturn('UK');

        $this->logger
            ->expects(self::once())
            ->method('info');

        $this->transferValidator
            ->expects(self::once())
            ->method('findExistingConsent')
            ->with($transfer)
            ->willReturn(null);

        $request = self::createMock(IpidValidationRequest::class);
        $this->requestBuilder
            ->expects(self::once())
            ->method('buildRequest')
            ->with($transfer)
            ->willReturn($request);

        $this->cacheManager
            ->expects(self::once())
            ->method('calculateRequestHash')
            ->willReturn('test-hash');

        $this->cacheManager
            ->expects(self::once())
            ->method('findValidCachedResponse')
            ->willReturn(null);

        $apiException = new IpidApiException('API error', 'IPID_001');
        $this->apiClient
            ->expects(self::once())
            ->method('sendValidationRequest')
            ->with($request)
            ->willThrowException($apiException);

        $expectedResult = self::createMock(IpidValidationResult::class);
        $this->errorHandler
            ->expects(self::once())
            ->method('handleApiException')
            ->with($apiException, $transfer)
            ->willReturn($expectedResult);

        $result = $this->service->validateTransfer($transfer);

        self::assertSame($expectedResult, $result);
    }
}