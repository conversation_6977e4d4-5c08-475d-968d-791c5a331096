<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;
use PHPUnit\Framework\TestCase;

final class IpidValidationResultTest extends TestCase
{
    public function testCreateSuccess(): void
    {
        $consent = self::createMock(TransferIpidConsent::class);
        $apiResponse = self::createMock(IpidApiResponse::class);

        $result = IpidValidationResult::createSuccess($consent, $apiResponse);

        self::assertEquals(IpidValidationResult::STATUS_SUCCESS, $result->getStatus());
        self::assertSame($consent, $result->getConsent());
        self::assertSame($apiResponse, $result->getApiResponse());
        self::assertNull($result->getErrorMessage());
        self::assertNull($result->getErrorCode());
        self::assertTrue($result->isSuccessful());
        self::assertFalse($result->isError());
        self::assertFalse($result->isRetryable());
        self::assertTrue($result->canProceed());
    }

    public function testCreateError(): void
    {
        $errorMessage = 'Validation failed';

        $result = IpidValidationResult::createError($errorMessage);

        self::assertEquals(IpidValidationResult::STATUS_ERROR, $result->getStatus());
        self::assertEquals($errorMessage, $result->getErrorMessage());
        self::assertNull($result->getConsent());
        self::assertNull($result->getApiResponse());
        self::assertNull($result->getErrorCode());
        self::assertFalse($result->isSuccessful());
        self::assertTrue($result->isError());
        self::assertFalse($result->isRetryable());
        self::assertFalse($result->canProceed());
    }

    public function testCreateServerError(): void
    {
        $errorMessage = 'Internal server error';
        $errorCode = 'IPID_500';

        $result = IpidValidationResult::createServerError($errorMessage, $errorCode);

        self::assertEquals(IpidValidationResult::STATUS_SERVER_ERROR, $result->getStatus());
        self::assertEquals($errorMessage, $result->getErrorMessage());
        self::assertEquals($errorCode, $result->getErrorCode());
        self::assertNull($result->getConsent());
        self::assertNull($result->getApiResponse());
        self::assertFalse($result->isSuccessful());
        self::assertTrue($result->isError());
        self::assertTrue($result->isServerError());
        self::assertFalse($result->isClientError());
        self::assertTrue($result->isRetryable());
        self::assertFalse($result->canProceed());
    }

    public function testCreateClientError(): void
    {
        $errorMessage = 'Bad request';
        $errorCode = 'IPID_400';

        $result = IpidValidationResult::createClientError($errorMessage, $errorCode);

        self::assertEquals(IpidValidationResult::STATUS_CLIENT_ERROR, $result->getStatus());
        self::assertEquals($errorMessage, $result->getErrorMessage());
        self::assertEquals($errorCode, $result->getErrorCode());
        self::assertNull($result->getConsent());
        self::assertNull($result->getApiResponse());
        self::assertFalse($result->isSuccessful());
        self::assertTrue($result->isError());
        self::assertFalse($result->isServerError());
        self::assertTrue($result->isClientError());
        self::assertFalse($result->isRetryable());
        self::assertFalse($result->canProceed());
    }

    public function testCreateValidationFailed(): void
    {
        $consent = self::createMock(TransferIpidConsent::class);
        $apiResponse = self::createMock(IpidApiResponse::class);
        $apiResponse
            ->expects(self::once())
            ->method('getResponseCode')
            ->willReturn('2104')
        ;

        $result = IpidValidationResult::createValidationFailed($consent, $apiResponse);

        self::assertEquals(IpidValidationResult::STATUS_VALIDATION_FAILED, $result->getStatus());
        self::assertEquals('2104', $result->getErrorCode());
        self::assertSame($consent, $result->getConsent());
        self::assertSame($apiResponse, $result->getApiResponse());
        self::assertNull($result->getErrorMessage());
        self::assertFalse($result->isSuccessful());
        self::assertTrue($result->isError());
        self::assertTrue($result->isValidationFailed());
        self::assertFalse($result->canProceed());
    }

    public function testCreateUnsupportedCountry(): void
    {
        $country = 'XX';

        $result = IpidValidationResult::createUnsupportedCountry($country);

        self::assertEquals(IpidValidationResult::STATUS_UNSUPPORTED_COUNTRY, $result->getStatus());
        self::assertEquals("Country $country is not supported for iPiD validation", $result->getErrorMessage());
        self::assertNull($result->getConsent());
        self::assertNull($result->getApiResponse());
        self::assertNull($result->getErrorCode());
        self::assertFalse($result->isSuccessful());
        self::assertFalse($result->isError());
        self::assertTrue($result->isUnsupportedCountry());
        self::assertFalse($result->canProceed());
    }

    public function testCreateAlreadyValidated(): void
    {
        $consent = self::createMock(TransferIpidConsent::class);

        $result = IpidValidationResult::createAlreadyValidated($consent);

        self::assertEquals(IpidValidationResult::STATUS_ALREADY_VALIDATED, $result->getStatus());
        self::assertSame($consent, $result->getConsent());
        self::assertNull($result->getApiResponse());
        self::assertNull($result->getErrorMessage());
        self::assertNull($result->getErrorCode());
        self::assertFalse($result->isSuccessful());
        self::assertFalse($result->isError());
        self::assertTrue($result->isAlreadyValidated());
        self::assertTrue($result->canProceed());
    }

    /**
     * @dataProvider statusCheckDataProvider
     */
    public function testStatusChecks(
        string $status,
        bool $isSuccessful,
        bool $isError,
        bool $isServerError,
        bool $isClientError,
        bool $isValidationFailed,
        bool $isAlreadyValidated,
        bool $isUnsupportedCountry,
        bool $canProceed
    ): void {
        $result = $this->createResultWithStatus($status);

        self::assertEquals($isSuccessful, $result->isSuccessful());
        self::assertEquals($isError, $result->isError());
        self::assertEquals($isServerError, $result->isServerError());
        self::assertEquals($isClientError, $result->isClientError());
        self::assertEquals($isValidationFailed, $result->isValidationFailed());
        self::assertEquals($isAlreadyValidated, $result->isAlreadyValidated());
        self::assertEquals($isUnsupportedCountry, $result->isUnsupportedCountry());
        self::assertEquals($canProceed, $result->canProceed());
    }

    public function statusCheckDataProvider(): array
    {
        return [
            'Success status' => [
                'status' => IpidValidationResult::STATUS_SUCCESS,
                'isSuccessful' => true,
                'isError' => false,
                'isServerError' => false,
                'isClientError' => false,
                'isValidationFailed' => false,
                'isAlreadyValidated' => false,
                'isUnsupportedCountry' => false,
                'canProceed' => true,
            ],
            'Error status' => [
                'status' => IpidValidationResult::STATUS_ERROR,
                'isSuccessful' => false,
                'isError' => true,
                'isServerError' => false,
                'isClientError' => false,
                'isValidationFailed' => false,
                'isAlreadyValidated' => false,
                'isUnsupportedCountry' => false,
                'canProceed' => false,
            ],
            'Server error status' => [
                'status' => IpidValidationResult::STATUS_SERVER_ERROR,
                'isSuccessful' => false,
                'isError' => true,
                'isServerError' => true,
                'isClientError' => false,
                'isValidationFailed' => false,
                'isAlreadyValidated' => false,
                'isUnsupportedCountry' => false,
                'canProceed' => false,
            ],
            'Client error status' => [
                'status' => IpidValidationResult::STATUS_CLIENT_ERROR,
                'isSuccessful' => false,
                'isError' => true,
                'isServerError' => false,
                'isClientError' => true,
                'isValidationFailed' => false,
                'isAlreadyValidated' => false,
                'isUnsupportedCountry' => false,
                'canProceed' => false,
            ],
            'Validation failed status' => [
                'status' => IpidValidationResult::STATUS_VALIDATION_FAILED,
                'isSuccessful' => false,
                'isError' => true,
                'isServerError' => false,
                'isClientError' => false,
                'isValidationFailed' => true,
                'isAlreadyValidated' => false,
                'isUnsupportedCountry' => false,
                'canProceed' => false,
            ],
            'Already validated status' => [
                'status' => IpidValidationResult::STATUS_ALREADY_VALIDATED,
                'isSuccessful' => false,
                'isError' => false,
                'isServerError' => false,
                'isClientError' => false,
                'isValidationFailed' => false,
                'isAlreadyValidated' => true,
                'isUnsupportedCountry' => false,
                'canProceed' => true,
            ],
            'Unsupported country status' => [
                'status' => IpidValidationResult::STATUS_UNSUPPORTED_COUNTRY,
                'isSuccessful' => false,
                'isError' => false,
                'isServerError' => false,
                'isClientError' => false,
                'isValidationFailed' => false,
                'isAlreadyValidated' => false,
                'isUnsupportedCountry' => true,
                'canProceed' => false,
            ],
        ];
    }

    public function testRequiresManualReviewWithConsentThatRequiresReview(): void
    {
        $consent = self::createMock(TransferIpidConsent::class);
        $consent
            ->expects(self::once())
            ->method('requiresManualReview')
            ->willReturn(true)
        ;
        $apiResponse = self::createMock(IpidApiResponse::class);

        $result = IpidValidationResult::createSuccess($consent, $apiResponse);

        self::assertTrue($result->requiresManualReview());
    }

    public function testRequiresManualReviewWithConsentThatDoesNotRequireReview(): void
    {
        $consent = self::createMock(TransferIpidConsent::class);
        $consent
            ->expects(self::once())
            ->method('requiresManualReview')
            ->willReturn(false)
        ;
        $apiResponse = self::createMock(IpidApiResponse::class);

        $result = IpidValidationResult::createSuccess($consent, $apiResponse);

        self::assertFalse($result->requiresManualReview());
    }

    public function testRequiresManualReviewWithoutConsent(): void
    {
        $result = IpidValidationResult::createError('Some error');

        self::assertFalse($result->requiresManualReview());
    }

    public function testStatusConstants(): void
    {
        self::assertEquals('success', IpidValidationResult::STATUS_SUCCESS);
        self::assertEquals('error', IpidValidationResult::STATUS_ERROR);
        self::assertEquals('server_error', IpidValidationResult::STATUS_SERVER_ERROR);
        self::assertEquals('client_error', IpidValidationResult::STATUS_CLIENT_ERROR);
        self::assertEquals('validation_failed', IpidValidationResult::STATUS_VALIDATION_FAILED);
        self::assertEquals('unsupported_country', IpidValidationResult::STATUS_UNSUPPORTED_COUNTRY);
        self::assertEquals('already_validated', IpidValidationResult::STATUS_ALREADY_VALIDATED);
    }

    private function createResultWithStatus(string $status): IpidValidationResult
    {
        switch ($status) {
            case IpidValidationResult::STATUS_SUCCESS:
                return IpidValidationResult::createSuccess(
                    self::createMock(TransferIpidConsent::class),
                    self::createMock(IpidApiResponse::class)
                );
            case IpidValidationResult::STATUS_ERROR:
                return IpidValidationResult::createError('Test error');
            case IpidValidationResult::STATUS_SERVER_ERROR:
                return IpidValidationResult::createServerError('Server error', 'CODE');
            case IpidValidationResult::STATUS_CLIENT_ERROR:
                return IpidValidationResult::createClientError('Client error', 'CODE');
            case IpidValidationResult::STATUS_VALIDATION_FAILED:
                $apiResponse = self::createMock(IpidApiResponse::class);
                $apiResponse->method('getResponseCode')->willReturn('2104');
                return IpidValidationResult::createValidationFailed(
                    self::createMock(TransferIpidConsent::class),
                    $apiResponse
                );
            case IpidValidationResult::STATUS_ALREADY_VALIDATED:
                return IpidValidationResult::createAlreadyValidated(
                    self::createMock(TransferIpidConsent::class)
                );
            case IpidValidationResult::STATUS_UNSUPPORTED_COUNTRY:
                return IpidValidationResult::createUnsupportedCountry('XX');
            default:
                throw new \InvalidArgumentException("Unknown status: $status");
        }
    }
} 