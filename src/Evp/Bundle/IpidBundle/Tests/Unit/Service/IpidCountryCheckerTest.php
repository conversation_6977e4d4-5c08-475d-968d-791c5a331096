<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\IpidBundle\Service\IpidCountryChecker;
use Evp\Bundle\IpidBundle\Service\IpidCountryRegistry;
use PHPUnit\Framework\TestCase;

final class IpidCountryCheckerTest extends TestCase
{
    private IpidCountryChecker $countryChecker;
    private IpidCountryRegistry $countryRegistry;

    protected function setUp(): void
    {
        $this->countryRegistry = new IpidCountryRegistry([], ['EU']);
        $this->countryChecker = new IpidCountryChecker($this->countryRegistry);
    }

    /**
     * @dataProvider euCountryValidationDataProvider
     */
    public function testEuCountryValidation(
        string $countryCode,
        bool $expectedEnabled,
        string $expectedScheme
    ): void {
        $isEnabled = $this->countryChecker->isCountryEnabled($countryCode);
        $scheme = $this->countryChecker->getValidationSchemeForCountry($countryCode);

        self::assertEquals($expectedEnabled, $isEnabled, "Country {$countryCode} enabled status mismatch");
        self::assertEquals($expectedScheme, $scheme, "Country {$countryCode} scheme mismatch");
    }

    public function euCountryValidationDataProvider(): array
    {
        return [
            // EU countries should be enabled with VOP scheme
            'Germany - EU VOP' => ['DE', true, 'VOP'],
            'France - EU VOP' => ['FR', true, 'VOP'],
            'Netherlands - EU VOP' => ['NL', true, 'VOP'],
            'Italy - EU VOP' => ['IT', true, 'VOP'],
            'Spain - EU VOP' => ['ES', true, 'VOP'],
            'Belgium - EU VOP' => ['BE', true, 'VOP'],
            'Austria - EU VOP' => ['AT', true, 'VOP'],
            'Poland - EU VOP' => ['PL', true, 'VOP'],
            'Czech Republic - EU VOP' => ['CZ', true, 'VOP'],
            'Slovakia - EU VOP' => ['SK', true, 'VOP'],
            'Slovenia - EU VOP' => ['SI', true, 'VOP'],
            'Portugal - EU VOP' => ['PT', true, 'VOP'],
            'Greece - EU VOP' => ['GR', true, 'VOP'],
            'Hungary - EU VOP' => ['HU', true, 'VOP'],
            'Croatia - EU VOP' => ['HR', true, 'VOP'],
            'Bulgaria - EU VOP' => ['BG', true, 'VOP'],
            'Romania - EU VOP' => ['RO', true, 'VOP'],
            'Estonia - EU VOP' => ['EE', true, 'VOP'],
            'Latvia - EU VOP' => ['LV', true, 'VOP'],
            'Lithuania - EU VOP' => ['LT', true, 'VOP'],
            'Finland - EU VOP' => ['FI', true, 'VOP'],
            'Sweden - EU VOP' => ['SE', true, 'VOP'],
            'Denmark - EU VOP' => ['DK', true, 'VOP'],
            'Ireland - EU VOP' => ['IE', true, 'VOP'],
            'Luxembourg - EU VOP' => ['LU', true, 'VOP'],
            'Malta - EU VOP' => ['MT', true, 'VOP'],
            'Cyprus - EU VOP' => ['CY', true, 'VOP'],

            // IBAN EU countries (non-Eurozone but SEPA)
            'Switzerland - EU VOP' => ['CH', true, 'VOP'],
            'Norway - EU VOP' => ['NO', true, 'VOP'],
            'Iceland - EU VOP' => ['IS', true, 'VOP'],
            'Liechtenstein - EU VOP' => ['LI', true, 'VOP'],
            'Monaco - EU VOP' => ['MC', true, 'VOP'],
            'San Marino - EU VOP' => ['SM', true, 'VOP'],
            'Vatican - EU VOP' => ['VA', true, 'VOP'],
            'Andorra - EU VOP' => ['AD', true, 'VOP'],

            // Non-EU countries should be disabled
            'United States - Not enabled' => ['US', false, 'GLOBAL'],
            'United Kingdom - Not enabled' => ['GB', false, 'COP'],
            'China - Not enabled' => ['CN', false, 'GLOBAL'],
            'India - Not enabled' => ['IN', false, 'GLOBAL'],
            'Brazil - Not enabled' => ['BR', false, 'GLOBAL'],
            'Canada - Not enabled' => ['CA', false, 'GLOBAL'],
            'Australia - Not enabled' => ['AU', false, 'GLOBAL'],
            'Japan - Not enabled' => ['JP', false, 'GLOBAL'],
            'South Korea - Not enabled' => ['KR', false, 'GLOBAL'],
            'Unknown country - Not enabled' => ['XX', false, 'GLOBAL'],
        ];
    }

    public function testIsRegionEnabled(): void
    {
        self::assertTrue($this->countryChecker->isRegionEnabled('EU'));
        self::assertFalse($this->countryChecker->isRegionEnabled('NORTH_AMERICA'));
        self::assertFalse($this->countryChecker->isRegionEnabled('ASIA_PACIFIC'));
        self::assertFalse($this->countryChecker->isRegionEnabled('UK'));
    }

    public function testGetEnabledCountries(): void
    {
        $enabledCountries = $this->countryChecker->getEnabledCountries();

        // Should contain all EU countries
        $expectedEuCountries = [
            'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR',
            'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL',
            'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE',
            'IS', 'LI', 'NO', 'CH', 'SM', 'VA', 'MC', 'AD'
        ];

        foreach ($expectedEuCountries as $country) {
            self::assertContains($country, $enabledCountries, "EU country {$country} should be enabled");
        }

        // Should not contain non-EU countries
        $nonEuCountries = ['US', 'GB', 'CN', 'IN', 'BR', 'CA', 'AU', 'JP'];
        foreach ($nonEuCountries as $country) {
            self::assertNotContains($country, $enabledCountries, "Non-EU country {$country} should not be enabled");
        }
    }

    public function testGetEnabledRegions(): void
    {
        $enabledRegions = $this->countryChecker->getEnabledRegions();

        self::assertEquals(['EU'], $enabledRegions);
    }

    /**
     * @dataProvider vopCountryDataProvider
     */
    public function testIsVopCountry(string $countryCode, bool $expectedIsVop): void
    {
        $result = $this->countryChecker->isVopCountry($countryCode);

        self::assertEquals($expectedIsVop, $result);
    }

    public function vopCountryDataProvider(): array
    {
        return [
            'Germany is VOP' => ['DE', true],
            'France is VOP' => ['FR', true],
            'Netherlands is VOP' => ['NL', true],
            'US is not VOP' => ['US', false],
            'UK is not VOP' => ['GB', false],
            'China is not VOP' => ['CN', false],
        ];
    }

    /**
     * @dataProvider copCountryDataProvider
     */
    public function testIsCopCountry(string $countryCode, bool $expectedIsCop): void
    {
        $result = $this->countryChecker->isCopCountry($countryCode);

        self::assertEquals($expectedIsCop, $result);
    }

    public function copCountryDataProvider(): array
    {
        return [
            'UK is COP' => ['GB', true],
            'UK variant is COP' => ['UK', true],
            'Germany is not COP' => ['DE', false],
            'France is not COP' => ['FR', false],
            'US is not COP' => ['US', false],
        ];
    }

    public function testSupportsValidation(): void
    {
        // EU countries should support validation
        self::assertTrue($this->countryChecker->supportsValidation('DE'));
        self::assertTrue($this->countryChecker->supportsValidation('FR'));
        self::assertTrue($this->countryChecker->supportsValidation('NL'));

        // Non-EU countries should not support validation
        self::assertFalse($this->countryChecker->supportsValidation('US'));
        self::assertFalse($this->countryChecker->supportsValidation('GB'));
        self::assertFalse($this->countryChecker->supportsValidation('CN'));
    }

    public function testGetRegionForCountry(): void
    {
        self::assertEquals('EU', $this->countryChecker->getRegionForCountry('DE'));
        self::assertEquals('EU', $this->countryChecker->getRegionForCountry('FR'));
        self::assertEquals('EU', $this->countryChecker->getRegionForCountry('NL'));
        self::assertEquals('UK', $this->countryChecker->getRegionForCountry('GB'));
        self::assertEquals('NORTH_AMERICA', $this->countryChecker->getRegionForCountry('US'));
        self::assertNull($this->countryChecker->getRegionForCountry('XX'));
    }

    public function testCaseInsensitivity(): void
    {
        // Test case insensitive country codes
        self::assertTrue($this->countryChecker->isCountryEnabled('de'));
        self::assertTrue($this->countryChecker->isCountryEnabled('DE'));
        self::assertTrue($this->countryChecker->isCountryEnabled('De'));
        self::assertTrue($this->countryChecker->isCountryEnabled('dE'));

        self::assertFalse($this->countryChecker->isCountryEnabled('us'));
        self::assertFalse($this->countryChecker->isCountryEnabled('US'));
        self::assertFalse($this->countryChecker->isCountryEnabled('Us'));
    }

    public function testWithCustomEnabledCountriesAndRegions(): void
    {
        // Test with both individual countries and regions enabled
        $registryWithCustomConfig = new IpidCountryRegistry(['US', 'CA'], ['EU']);
        $checkerWithCustomConfig = new IpidCountryChecker($registryWithCustomConfig);

        // Individual countries should be enabled
        self::assertTrue($checkerWithCustomConfig->isCountryEnabled('US'));
        self::assertTrue($checkerWithCustomConfig->isCountryEnabled('CA'));

        // EU countries should still be enabled
        self::assertTrue($checkerWithCustomConfig->isCountryEnabled('DE'));
        self::assertTrue($checkerWithCustomConfig->isCountryEnabled('FR'));

        // Other countries should not be enabled
        self::assertFalse($checkerWithCustomConfig->isCountryEnabled('GB'));
        self::assertFalse($checkerWithCustomConfig->isCountryEnabled('CN'));

        $enabledCountries = $checkerWithCustomConfig->getEnabledCountries();
        self::assertContains('US', $enabledCountries);
        self::assertContains('CA', $enabledCountries);
        self::assertContains('DE', $enabledCountries);
        self::assertContains('FR', $enabledCountries);
    }

    public function testEuConfigurationMatchesDocumentation(): void
    {
        // According to the documentation, EU supports VOP scheme
        // Test key EU countries mentioned in the docs
        $documentedEuCountries = [
            'DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'PL', 'PT', 'GR'
        ];

        foreach ($documentedEuCountries as $country) {
            self::assertTrue(
                $this->countryChecker->isCountryEnabled($country),
                "EU country {$country} should be enabled according to VOP documentation"
            );
            self::assertEquals(
                'VOP',
                $this->countryChecker->getValidationSchemeForCountry($country),
                "EU country {$country} should use VOP scheme"
            );
            self::assertTrue(
                $this->countryChecker->isVopCountry($country),
                "EU country {$country} should be identified as VOP country"
            );
        }
    }
}
