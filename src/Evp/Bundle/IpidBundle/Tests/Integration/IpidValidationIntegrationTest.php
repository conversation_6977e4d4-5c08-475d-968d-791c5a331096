<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Integration;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\Service\IpidValidationService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\XmlFileLoader;
use Symfony\Component\Config\FileLocator;

class IpidValidationIntegrationTest extends TestCase
{
    private ContainerBuilder $container;

    protected function setUp(): void
    {
        $this->container = new ContainerBuilder();

        // Load service definitions
        $loader = new XmlFileLoader($this->container, new FileLocator(__DIR__ . '/../../Resources/config'));
        $loader->load('services.xml');

        // Mock external dependencies that would normally be provided by other bundles
        $this->mockExternalDependencies();

        $this->container->compile();
    }

    public function testServiceCanBeInstantiated(): void
    {
        $validationService = $this->container->get('evp_ipid.validation_service');

        self::assertInstanceOf(IpidValidationService::class, $validationService);
    }

    public function testAllFocusedServicesAreWired(): void
    {
        $transferValidator = $this->container->get('evp_ipid.transfer_validator');
        $cacheManager = $this->container->get('evp_ipid.cache_manager');
        $responseProcessor = $this->container->get('evp_ipid.response_processor');
        $errorHandler = $this->container->get('evp_ipid.error_handler');

        self::assertNotNull($transferValidator);
        self::assertNotNull($cacheManager);
        self::assertNotNull($responseProcessor);
        self::assertNotNull($errorHandler);
    }

    private function mockExternalDependencies(): void
    {
        // Mock services that would be provided by other bundles
        $this->container->register('evp_ipid.country_checker', \stdClass::class);
        $this->container->register('evp_ipid.consent_repository', \stdClass::class);
        $this->container->register('evp_ipid.corridor_specification', \stdClass::class);
        $this->container->register('evp_ipid.api_response_repository', \stdClass::class);
        $this->container->register('evp_ipid.request_builder', \stdClass::class);
        $this->container->register('evp_ipid.api_client', \stdClass::class);
        $this->container->register('evp_ipid.transfer_helper', \stdClass::class);
        $this->container->register('logger', \stdClass::class);
    }
}
