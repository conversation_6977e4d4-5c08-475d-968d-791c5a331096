<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Functional;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\Service\IpidTransferHelper;
use Evp\Bundle\IpidBundle\Service\IpidCountryChecker;
use Evp\Bundle\IpidBundle\Service\IpidCountryRegistry;
use PHPUnit\Framework\TestCase;

/**
 * Functional tests for iPiD service integration.
 *
 * Tests real service integration and coordination:
 * - Transfer data extraction and country detection
 * - Service coordination and data flow
 * - Integration between helper services
 */
final class IpidServiceIntegrationTest extends TestCase
{
    private IpidTransferHelper $transferHelper;
    private IpidCountryChecker $countryChecker;

    protected function setUp(): void
    {
        $this->transferHelper = new IpidTransferHelper();

        // Create a mock country registry with realistic data
        $countryRegistry = self::createMock(IpidCountryRegistry::class);
        $countryRegistry->method('isCountryEnabled')->willReturnCallback(function ($country) {
            return in_array($country, ['GB', 'US', 'FR', 'DE', 'BE'], true);
        });
        $countryRegistry->method('getValidationSchemeForCountry')->willReturnCallback(function ($country) {
            switch ($country) {
                case 'GB':
                    return 'COP';
                case 'US':
                    return 'VOP';
                case 'FR':
                case 'DE':
                case 'BE':
                    return 'COP';
                default:
                    return 'GLOBAL';
            }
        });
        $countryRegistry->method('getRegionForCountry')->willReturnCallback(function ($country) {
            switch ($country) {
                case 'GB':
                    return 'UK';
                case 'US':
                    return 'US';
                case 'FR':
                case 'DE':
                case 'BE':
                    return 'EU';
                default:
                    return null;
            }
        });
        $countryRegistry->method('getEnabledCountries')->willReturn(['GB', 'US', 'FR', 'DE', 'BE']);
        $countryRegistry->method('getEnabledRegions')->willReturn(['UK', 'US', 'EU']);

        $this->countryChecker = new IpidCountryChecker($countryRegistry);
    }

    /**
     * @dataProvider transferDataExtractionScenarios
     */
    public function testTransferDataExtractionIntegration(
        string $scenario,
        array $transferData,
        array $expectedResults
    ): void {
        $transfer = $this->createTransferFromData($transferData);

        // Test country extraction
        $extractedCountry = $this->transferHelper->extractCountryFromTransfer($transfer);
        self::assertEquals($expectedResults['country'], $extractedCountry, "Country extraction failed for: $scenario");

        // Test country validation integration
        $isSupported = $this->countryChecker->isCountryEnabled($extractedCountry);
        self::assertEquals($expectedResults['is_supported'], $isSupported, "Country support check failed for: $scenario");

        // Test validation scheme detection
        $validationScheme = $this->countryChecker->getValidationSchemeForCountry($extractedCountry);
        self::assertEquals($expectedResults['validation_scheme'], $validationScheme, "Validation scheme detection failed for: $scenario");

        // Test region detection
        $region = $this->countryChecker->getRegionForCountry($extractedCountry);
        self::assertEquals($expectedResults['region'], $region, "Region detection failed for: $scenario");
    }

    public function transferDataExtractionScenarios(): array
    {
        return [
            'UK IBAN - COP validation' => [
                'scenario' => 'UK IBAN - COP validation',
                'transferData' => [
                    'iban' => '**********************',
                    'beneficiary_name' => 'John Smith',
                ],
                'expectedResults' => [
                    'country' => 'GB',
                    'is_supported' => true,
                    'validation_scheme' => 'COP',
                    'region' => 'UK',
                ],
            ],
            'US account - VOP validation' => [
                'scenario' => 'US account - VOP validation',
                'transferData' => [
                    'iban' => '********************',
                    'beneficiary_name' => 'Jane Doe',
                ],
                'expectedResults' => [
                    'country' => 'US',
                    'is_supported' => true,
                    'validation_scheme' => 'VOP',
                    'region' => 'US',
                ],
            ],
            'French IBAN - COP validation' => [
                'scenario' => 'French IBAN - COP validation',
                'transferData' => [
                    'iban' => '***************************',
                    'beneficiary_name' => 'Pierre Dubois',
                ],
                'expectedResults' => [
                    'country' => 'FR',
                    'is_supported' => true,
                    'validation_scheme' => 'COP',
                    'region' => 'EU',
                ],
            ],
            'Unsupported country' => [
                'scenario' => 'Unsupported country',
                'transferData' => [
                    'iban' => '********************',
                    'beneficiary_name' => 'Tanaka San',
                ],
                'expectedResults' => [
                    'country' => 'JP',
                    'is_supported' => false,
                    'validation_scheme' => 'GLOBAL',
                    'region' => null,
                ],
            ],
        ];
    }

    public function testCountryCheckerServiceIntegration(): void
    {
        // Test that all country checker methods work together consistently
        $enabledCountries = $this->countryChecker->getEnabledCountries();
        $enabledRegions = $this->countryChecker->getEnabledRegions();

        self::assertIsArray($enabledCountries);
        self::assertIsArray($enabledRegions);
        self::assertNotEmpty($enabledCountries);
        self::assertNotEmpty($enabledRegions);

        // Test that each enabled country has a validation scheme
        foreach ($enabledCountries as $country) {
            $scheme = $this->countryChecker->getValidationSchemeForCountry($country);
            self::assertNotEmpty($scheme, "Country $country should have a validation scheme");
            self::assertContains($scheme, ['COP', 'VOP', 'GLOBAL'], "Invalid validation scheme for $country: $scheme");
        }

        // Test VOP/COP detection methods
        self::assertTrue($this->countryChecker->isCopCountry('GB'));
        self::assertTrue($this->countryChecker->isVopCountry('US'));
        self::assertFalse($this->countryChecker->isVopCountry('GB'));
        self::assertFalse($this->countryChecker->isCopCountry('US'));
    }

    public function testTransferHelperDataExtraction(): void
    {
        // Test various IBAN formats and data extraction
        $testCases = [
            ['**********************', 'GB'],
            ['***************************', 'FR'],
            ['**********************', 'DE'],
            ['****************', 'BE'],
            ['********************', 'US'],
        ];

        foreach ($testCases as [$iban, $expectedCountry]) {
            $transfer = $this->createTransferFromData(['iban' => $iban, 'beneficiary_name' => 'Test User']);
            $extractedCountry = $this->transferHelper->extractCountryFromTransfer($transfer);

            self::assertEquals($expectedCountry, $extractedCountry, "Failed to extract country from IBAN: $iban");
        }
    }

    public function testServiceCoordinationWorkflow(): void
    {
        // Test a complete workflow using multiple services
        $transfer = $this->createTransferFromData([
            'iban' => '**********************',
            'beneficiary_name' => 'Integration Test User',
        ]);

        // Step 1: Extract country
        $country = $this->transferHelper->extractCountryFromTransfer($transfer);
        self::assertEquals('GB', $country);

        // Step 2: Check if country is supported
        $isSupported = $this->countryChecker->isCountryEnabled($country);
        self::assertTrue($isSupported);

        // Step 3: Get validation scheme
        $scheme = $this->countryChecker->getValidationSchemeForCountry($country);
        self::assertEquals('COP', $scheme);

        // Step 4: Verify it's a COP country
        self::assertTrue($this->countryChecker->isCopCountry($country));
        self::assertFalse($this->countryChecker->isVopCountry($country));

        // Step 5: Get region
        $region = $this->countryChecker->getRegionForCountry($country);
        self::assertEquals('UK', $region);

        // Step 6: Verify validation support
        self::assertTrue($this->countryChecker->isCountryEnabled($country));
    }

    private function createTransferFromData(array $data): TransferOut
    {
        $transfer = self::createMock(TransferOut::class);

        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn($data['iban']);
        $beneficiary->method('getName')->willReturn($data['beneficiary_name']);

        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        return $transfer;
    }
}
